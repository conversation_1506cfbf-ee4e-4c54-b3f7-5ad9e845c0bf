[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:增加一款智能体工具“请求人工帮助” DESCRIPTION: 在src/services/terminalTools.ts中，已经定义了一些OpenAI格式的工具；
本次新增的工具为“请求人工帮助”；请新建src/services/tools/目录实现该工具，并在terminalTools.ts中import；防止工具都写在terminalTools.ts中，导致文件过长。
"请求人工帮助"是一款智能体在遇到无法决策、频繁卡在某个场景、需要用户提供更多信息时，请求人工帮助的工具。
但是智能体需描述当前的问题，请求人工何种帮助，并且将需要人工帮助的需求转化为一个html form表单，通过让人工填写表单，来提供更多信息。
工具输入参数为：
- 可渲染的html文本代码（string类型，要求是一个可正常渲染的html文本，包含问题或需求描述内容、待用户填写的表单内容、确定/取消按钮； 确定按钮点击后执行一个预定义在window全局的函数，将用户表单内容收集为json，作为本次工具执行结果返回； 取消按钮点击后，同样执行全局函数，标识用户取消了帮助请求，作为工具返回）
- 超时时间（单位：秒；等待人工帮助的时长）；
本工具执行时，直接进入超时等待；取消等待的钩子函数注册在window全局。若超时，则本工具执行结果即为超时未获得用户帮助；若超时被取消，则从全局函数获取帮助结果（用户可能填写也可能取消）返回。

定义一个windows全局的管理器对象，提供以上辅助功能：注册取消超时钩子函数，存储确定、取消信息，获取信息；



-[ ] NAME: 修正src/services/terminalTools.js文件 DESCRIPTION:terminalTools.js文件拷贝自旧工程，并未与当前工程完全整合。先重命名为：terminalTools.ts；它的作用是：封装xtermService.ts中的终端方法为agent智能体工具，工具定义采用OpenAI的工具定义模式。确保该模块文件无编译错误。

-[ ] NAME: 修正src/services/refactoredAIAgent.js文件 DESCRIPTION:refactoredAIAgent.js文件拷贝自旧工程，并未与当前工程完全整合。先重命名为：AIAgent.ts；它的作用是：实现了一个agent智能体模式，使用OpenAI SDK，支持工具调用。确保该模块文件无编译错误。

-[ ] NAME: 重构src/stores/aiStore.ts模块 DESCRIPTION: aiStore.ts 包含了 AIDialog UI 依赖的一些状态数据，但是缺乏对工具调用消息的支持，请参考src/stores/aiMessages.example.txt文件中handleStreamingChunk的代码，chunk是OpenAI SDK chat流式数据，包含工具调用信息。参考aiMessages.example.txt文件丰富 aiStore.ts 对工具调用的支持。后续AIDialog UI也需要支持工具调用消息的渲染。

-[ ] NAME: 重构 aiService.ts 模块 sendMessage 方法，使其与 AIAgent 集成 DESCRIPTION:前面的工作已经完成了AIAgent的修正，要将它与本工程代码集成。集成的位置就在于sendMessage。注意会话的管理；AI流式响应、工具调用流式响应的处理；对 aiStore.ts 的数据更新。

-[ ] NAME: 丰富 AIDialog UI，支持工具调用消息的渲染 DESCRIPTION:前面的重构任务中aiStore已经增加了工具的消息，需要在AIDialog中体现，可能涉及新的工具调用UI展示组件。同时检查当前普通AI流式消息渲染是否正常。

