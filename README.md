# Web 终端

一个基于 Web 的安全终端应用，使用 Go 后端和 xterm.js 前端构建。

## 功能特性

- 基于浏览器的终端模拟器
- 支持多标签页
- 响应式设计，适配不同屏幕尺寸
- IP 白名单安全机制
- 可配置的运行时间限制

## 快速开始

### 运行要求

- Linux 系统 (AMD64 架构)
- 无需安装额外依赖

### 使用方法

1. 下载并解压发布包
2. 进入解压目录
3. 运行以下命令启动服务：

```bash
# 基本用法
./web-terminal

# 指定端口 (默认: 8080)
./web-terminal -port 9999

# 重置IP白名单
./web-terminal -reset-whitelist

# 设置运行时间（分钟）
./web-terminal -runtime 60

# 禁用运行时间限制
./web-terminal -no-expiry

# 组合使用
./web-terminal -port 9999 -runtime 30 -reset-whitelist
```

### 参数说明

- `-port` : 指定服务端口，默认为 8080
- `-reset-whitelist` : 重置IP白名单
- `-runtime` : 设置服务运行时间（分钟）
- `-no-expiry` : 禁用运行时间限制
- `-frontend` : 指定前端资源目录（通常不需要手动指定）

## 构建说明

### 开发环境

- Go 1.16+
- Node.js 14+
- npm 或 yarn

### 构建命令

```bash
# 安装前端依赖
cd frontend
npm install

# 构建前端
npm run build

# 构建后端 (Linux/AMD64)
cd ..
GOOS=linux GOARCH=amd64 go build -o dist/web-terminal ./backend/cmd

# 复制前端资源
cp -r frontend/dist/* dist/frontend/
```

## 安全说明

- 默认情况下，第一个连接的客户端IP会被加入白名单
- 之后的连接只允许来自白名单IP的访问
- 使用 `-reset-whitelist` 参数可以重置白名单
- 建议在生产环境中使用防火墙限制访问
