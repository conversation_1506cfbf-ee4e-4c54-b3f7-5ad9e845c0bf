package terminal

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"os"
	"os/exec"
	"sync"

	"github.com/creack/pty"
	"github.com/gorilla/websocket"
)

// Terminal represents a terminal instance
type Terminal struct {
	cmd *exec.Cmd
	pty *os.File
	ws  *websocket.Conn
	mu  sync.Mutex
}

// Message represents a message exchanged between client and server
type Message struct {
	Type string `json:"type"`
	Data string `json:"data"`
}

// NewTerminal creates a new terminal instance
func NewTerminal(ws *websocket.Conn) (*Terminal, error) {
	// Create command to run shell
	shellPath := os.Getenv("SHELL")
	if shellPath == "" {
		shellPath = "/bin/bash"
	}
	
	cmd := exec.Command(shellPath)
	
	// Set environment variables
	cmd.Env = append(os.Environ(), "TERM=xterm-256color")
	
	// Start pty
	ptmx, err := pty.Start(cmd)
	if err != nil {
		return nil, fmt.Errorf("failed to start pty: %v", err)
	}
	
	return &Terminal{
		cmd: cmd,
		pty: ptmx,
		ws:  ws,
	}, nil
}

// Start starts the terminal and handles communication
func (t *Terminal) Start() error {
	// Create a channel to signal when process exits
	done := make(chan error, 1)
	
	// Start goroutine to handle pty -> websocket communication
	go func() {
		buf := make([]byte, 1024)
		for {
			n, err := t.pty.Read(buf)
			if err != nil {
				if err != io.EOF {
					done <- fmt.Errorf("read from pty error: %v", err)
				}
				done <- nil
				return
			}
			
			output := string(buf[:n])
			log.Printf("[Terminal] Sending output to client: %q (length: %d)", output, len(output))

			t.mu.Lock()
			err = t.ws.WriteJSON(Message{
				Type: "output",
				Data: output,
			})
			t.mu.Unlock()
			
			if err != nil {
				done <- fmt.Errorf("write to websocket error: %v", err)
				return
			}
		}
	}()
	
	// Start goroutine to handle websocket -> pty communication
	go func() {
		for {
			var msg Message
			err := t.ws.ReadJSON(&msg)
			if err != nil {
				done <- fmt.Errorf("read from websocket error: %v", err)
				return
			}
			
			log.Printf("[Terminal] Received message: type=%s, data=%q (length: %d)", msg.Type, msg.Data, len(msg.Data))

			switch msg.Type {
			case "input":
				input := []byte(msg.Data)
				log.Printf("[Terminal] Writing input to pty: %q (bytes: %v)", msg.Data, input)
				_, err = t.pty.Write(input)
				if err != nil {
					done <- fmt.Errorf("write to pty error: %v", err)
					return
				}
			case "resize":
				var size struct {
					Cols int `json:"cols"`
					Rows int `json:"rows"`
				}
				if err := json.Unmarshal([]byte(msg.Data), &size); err != nil {
					continue
				}
				
				if err := pty.Setsize(t.pty, &pty.Winsize{
					Rows: uint16(size.Rows),
					Cols: uint16(size.Cols),
				}); err != nil {
					continue
				}
			}
		}
	}()
	
	// Wait for done signal
	return <-done
}

// Close closes the terminal
func (t *Terminal) Close() error {
	if t.pty != nil {
		t.pty.Close()
	}
	if t.cmd != nil && t.cmd.Process != nil {
		t.cmd.Process.Kill()
		t.cmd.Wait()
	}
	return nil
}
