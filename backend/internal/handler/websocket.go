package handler

import (
	"log"
	"net/http"
	"web-terminal/internal/terminal"

	"github.com/gorilla/websocket"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // Allow all connections (for development, can be restricted in production)
	},
}

// TerminalHandler handles WebSocket connections for terminal sessions
func TerminalHandler(w http.ResponseWriter, r *http.Request) {
	// Upgrade HTTP connection to WebSocket
	ws, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("Error upgrading to websocket: %v", err)
		return
	}
	defer ws.Close()

	log.Println("WebSocket connection established")

	// Create new terminal session
	term, err := terminal.NewTerminal(ws)
	if err != nil {
		log.Printf("Error creating terminal: %v", err)
		ws.WriteJSON(map[string]string{
			"type": "error",
			"data": err.Error(),
		})
		return
	}
	defer term.Close()

	// Start terminal session
	if err := term.Start(); err != nil {
		log.Printf("Terminal session ended with error: %v", err)
		return
	}

	log.Println("Terminal session ended")
}
