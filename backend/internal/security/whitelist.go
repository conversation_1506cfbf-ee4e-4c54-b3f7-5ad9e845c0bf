package security

import (
	"log"
	"net"
	"net/http"
	"sync"
)

// IPWhitelist manages a list of allowed IP addresses
type IPWhitelist struct {
	allowedIPs map[string]bool
	isInitialized bool // Indicates if the first IP has been added
	mutex      sync.RWMutex
}

// NewIPWhitelist creates a new IP whitelist
func NewIPWhitelist() *IPWhitelist {
	return &IPWhitelist{
		allowedIPs: make(map[string]bool),
		isInitialized: false,
	}
}

// IsAllowed checks if an IP is allowed
func (wl *IPWhitelist) IsAllowed(ip string) bool {
	wl.mutex.RLock()
	defer wl.mutex.RUnlock()
	
	// If the whitelist hasn't been initialized, allow all IPs
	if !wl.isInitialized {
		return true
	}
	
	return wl.allowedIPs[ip]
}

// AddIP adds an IP to the whitelist
func (wl *IPWhitelist) AddIP(ip string) {
	wl.mutex.Lock()
	defer wl.mutex.Unlock()
	
	wl.allowedIPs[ip] = true
	wl.isInitialized = true
	log.Printf("IP added to whitelist: %s", ip)
}

// RemoveIP removes an IP from the whitelist
func (wl *IPWhitelist) RemoveIP(ip string) {
	wl.mutex.Lock()
	defer wl.mutex.Unlock()
	
	delete(wl.allowedIPs, ip)
	log.Printf("IP removed from whitelist: %s", ip)
	
	// If the last IP is removed, reset initialization status
	if len(wl.allowedIPs) == 0 {
		wl.isInitialized = false
	}
}

// ListAllowedIPs returns a slice of all allowed IPs
func (wl *IPWhitelist) ListAllowedIPs() []string {
	wl.mutex.RLock()
	defer wl.mutex.RUnlock()
	
	ips := make([]string, 0, len(wl.allowedIPs))
	for ip := range wl.allowedIPs {
		ips = append(ips, ip)
	}
	
	return ips
}

// Reset clears the whitelist
func (wl *IPWhitelist) Reset() {
	wl.mutex.Lock()
	defer wl.mutex.Unlock()
	
	wl.allowedIPs = make(map[string]bool)
	wl.isInitialized = false
	log.Println("IP whitelist reset")
}

// IsInitialized returns whether the whitelist has been initialized
func (wl *IPWhitelist) IsInitialized() bool {
	wl.mutex.RLock()
	defer wl.mutex.RUnlock()
	return wl.isInitialized
}

// Middleware creates an HTTP middleware that restricts access based on IP whitelist
func (wl *IPWhitelist) Middleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Extract client IP
		ip, err := GetClientIP(r)
		if err != nil {
			http.Error(w, "Failed to determine client IP", http.StatusInternalServerError)
			return
		}
		
		// Check if whitelist is initialized
		if !wl.IsInitialized() {
			// If this is the first access, add IP to whitelist
			wl.AddIP(ip)
			log.Printf("First access detected. Initialized whitelist with IP: %s", ip)
			next.ServeHTTP(w, r)
			return
		}
		
		// Check if IP is allowed
		if wl.IsAllowed(ip) {
			next.ServeHTTP(w, r)
		} else {
			log.Printf("Access denied for IP: %s", ip)
			http.Error(w, "Access denied", http.StatusForbidden)
		}
	})
}

// GetClientIP extracts the client's real IP address from the request
func GetClientIP(r *http.Request) (string, error) {
	// Check for X-Forwarded-For header (common when behind proxies)
	ip := r.Header.Get("X-Forwarded-For")
	if ip != "" {
		// X-Forwarded-For can contain multiple IPs, take the first one
		ips := net.ParseIP(ip)
		if ips != nil {
			return ips.String(), nil
		}
	}
	
	// Check for X-Real-IP header (another common proxy header)
	ip = r.Header.Get("X-Real-IP")
	if ip != "" {
		ips := net.ParseIP(ip)
		if ips != nil {
			return ips.String(), nil
		}
	}
	
	// Get the remote address directly
	ipStr, _, err := net.SplitHostPort(r.RemoteAddr)
	if err != nil {
		return "", err
	}
	
	return ipStr, nil
}
