package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"path/filepath"
	"strings"
	"syscall"
	"time"

	"web-terminal/internal/handler"
	"web-terminal/internal/monitoring"
	"web-terminal/internal/security"
)

// File constants
const (
	WhitelistFile = "whitelist.json"  // IP whitelist storage
	RuntimeFile   = "runtime.json"     // Runtime extension storage
)

// WhitelistData represents the structure of the whitelist JSON file
type WhitelistData struct {
	IPs []string `json:"ips"`
}

// Helper function to check if a file exists
func fileExists(filename string) bool {
	_, err := os.Stat(filename)
	return !os.IsNotExist(err)
}

// Helper function to check if a directory exists
func dirExists(path string) bool {
	info, err := os.Stat(path)
	if err != nil {
		return false
	}
	return info.IsDir()
}

// Helper function to get current working directory
func getCurrentDir() string {
	wd, err := os.Getwd()
	if err != nil {
		return fmt.Sprintf("error: %v", err)
	}
	return wd
}

// Helper function to check if a port is in use
func isPortInUse(port int) bool {
	timeout := time.Second
	conn, err := net.DialTimeout("tcp", fmt.Sprintf(":%d", port), timeout)
	if err != nil {
		return false
	}
	if conn != nil {
		conn.Close()
		return true
	}
	return false
}

func main() {
	// Parse command line flags
	port := flag.Int("port", 8081, "Port to listen on")
	frontendPath := flag.String("frontend", "../frontend/dist", "Path to frontend assets")
	whitelistReset := flag.Bool("reset-whitelist", false, "Reset the IP whitelist on startup")
	
	// Runtime monitoring flags
	runtimeDuration := flag.Int("runtime", 60, "Default runtime in minutes before automatic shutdown")
	noExpiry := flag.Bool("no-expiry", false, "Disable runtime expiration")
	
	flag.Parse()

	// Check if frontend path exists
	frontendAbsPath, err := filepath.Abs(*frontendPath)
	if err != nil {
		log.Fatalf("Error resolving frontend path: %v", err)
	}

	// Check if the specified frontend directory exists
	if _, err := os.Stat(frontendAbsPath); os.IsNotExist(err) {
		// If the specified path doesn't exist, try some alternatives
		alternatives := []string{
			"./frontend/dist",
			"./frontend",
			"../frontend/dist",
		}

		for _, altPath := range alternatives {
			if _, err := os.Stat(altPath); err == nil {
				frontendAbsPath = altPath
				break
			}
		}
	}

	log.Printf("Serving frontend assets from: %s", frontendAbsPath)

	// Create IP whitelist
	ipWhitelist := security.NewIPWhitelist()

	// Load whitelist from file if it exists (unless reset flag is set)
	if !*whitelistReset {
		loadIPWhitelist(ipWhitelist)
	} else {
		log.Println("Whitelist reset requested. Starting with empty whitelist.")
		// Delete the whitelist file if it exists
		if _, err := os.Stat(WhitelistFile); err == nil {
			os.Remove(WhitelistFile)
		}
	}

	// Set up handlers with the whitelist middleware
	fileServer := http.FileServer(http.Dir(frontendAbsPath))

	// Create a new ServeMux to register handlers with middleware
	mux := http.NewServeMux()

	// Apply whitelist middleware to all routes
	mux.Handle("/", ipWhitelist.Middleware(fileServer))
	
	// Set up WebSocket handler with IP whitelist check
	mux.HandleFunc("/ws", func(w http.ResponseWriter, r *http.Request) {
		// Extract client IP
		ip, err := security.GetClientIP(r)
		if err != nil {
			http.Error(w, "Failed to determine client IP", http.StatusInternalServerError)
			return
		}

		// Check if IP is allowed
		if !ipWhitelist.IsAllowed(ip) {
			log.Printf("WebSocket access denied for IP: %s", ip)
			http.Error(w, "Access denied", http.StatusForbidden)
			return
		}

		// IP is allowed, serve the WebSocket request
		handler.TerminalHandler(w, r)

		// Save the whitelist after successful connection
		saveIPWhitelist(ipWhitelist)
	})

	// Create runtime monitor if not disabled
	var runtimeMonitor *monitoring.RuntimeMonitor
	if !*noExpiry {
		// Convert minutes to duration
		duration := time.Duration(*runtimeDuration) * time.Minute
		
		// Create shutdown function
		shutdownFunc := func() {
			log.Println("Runtime expired, shutting down server")
			// Allow a grace period before forcing exit
			time.Sleep(5 * time.Second)
			os.Exit(0)
		}
		
		// Create and start runtime monitor
		log.Println("1. Creating runtime monitor...")
		runtimeMonitor = monitoring.NewRuntimeMonitor(duration, RuntimeFile, shutdownFunc)
		log.Println("2. Calling runtimeMonitor.Start()...")
		runtimeMonitor.Start()
		log.Printf("3. Runtime monitoring enabled. Server will shut down after %s", 
			formatDuration(duration))
	} else {
		log.Println("Runtime monitoring disabled. Server will run indefinitely.")
	}
	
	// Add runtime monitoring endpoints
	log.Println("4. Setting up runtime monitoring endpoints...")
	if runtimeMonitor != nil {
		log.Println("4.1 Adding /api/extend endpoint")
		mux.HandleFunc("/api/extend", runtimeMonitor.ExtensionHandler)
		
		log.Println("4.2 Adding /api/status endpoint")
		mux.HandleFunc("/api/status", runtimeMonitor.StatusHandler)
	} else {
		log.Println("4.0 Warning: runtimeMonitor is nil, skipping endpoint setup")
	}

	// Create a server with graceful shutdown
	log.Println("5. Setting up HTTP server...")
	// Use explicit 0.0.0.0 binding to ensure the server listens on all available network interfaces
	listenAddr := fmt.Sprintf("0.0.0.0:%d", *port)
	log.Printf("5.1 Configuring HTTP server to listen on: %s", listenAddr)
	
	// Create a ready channel to signal when server is actually listening
	readyChan := make(chan struct{})
	
	// Create an error channel to catch startup errors
	errChan := make(chan error, 1)
	
	// Create HTTP server with timeouts
	srv := &http.Server{
		Addr:         listenAddr,
		Handler:      mux,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  120 * time.Second,
		ErrorLog:     log.New(os.Stderr, "[HTTP] ", log.LstdFlags),
	}

	// Setup graceful shutdown
	stopChan := make(chan os.Signal, 1)
	signal.Notify(stopChan, os.Interrupt, syscall.SIGTERM)

	// Start the server in a goroutine
	go func() {
		log.Printf("\n=== SERVER STARTUP DEBUG ===")
		log.Printf("1. Preparing to start server on 0.0.0.0:%d...", *port)
		log.Printf("1.1 Current working directory: %s", getCurrentDir())
		log.Printf("1.2 Frontend path: %s (exists: %v)", frontendAbsPath, dirExists(frontendAbsPath))
		log.Printf("1.3 Whitelist file: %s (exists: %v)", WhitelistFile, fileExists(WhitelistFile))
		log.Printf("1.4 Runtime file: %s (exists: %v)", RuntimeFile, fileExists(RuntimeFile))
		log.Printf("1.5 Command line arguments: %v", os.Args)
		
		log.Printf("\n2. Security Configuration:")
		log.Printf("2.1 First client IP to connect will be whitelisted")
		if ipWhitelist.IsInitialized() {
			log.Printf("2.2 Whitelist already initialized with %d IPs: %v", 
				len(ipWhitelist.ListAllowedIPs()), ipWhitelist.ListAllowedIPs())
		} else {
			log.Printf("2.2 Whitelist not yet initialized")
		}

		// Start listener manually to ensure we're actually bound before signaling
		log.Printf("3. Attempting to create TCP listener on 0.0.0.0:%d...", *port)
		
		// Log network interfaces and routing information
		log.Printf("\n3. Network Configuration:")
		
		// 3.1 List network interfaces
		log.Printf("3.1 Network Interfaces:")
		ifaces, err := net.Interfaces()
		if err != nil {
			log.Printf("3.1.1 ERROR: Could not list network interfaces: %v", err)
		} else {
			for i, iface := range ifaces {
				addrs, _ := iface.Addrs()
				log.Printf("3.1.%d %s: HW: %s, UP: %t, MULTICAST: %t, Addrs: %v", 
					i+1, 
					iface.Name, 
					iface.HardwareAddr,
					iface.Flags&net.FlagUp != 0,
					iface.Flags&net.FlagMulticast != 0,
					addrs)
			}
		}
		
		// 3.2 Check if port is already in use
		log.Printf("\n3.2 Checking if port %d is already in use...", *port)
		if isPortInUse(*port) {
			log.Printf("3.2.1 WARNING: Port %d is already in use!", *port)
		} else {
			log.Printf("3.2.1 Port %d is available", *port)
		}

		// 4. Create TCP listener with detailed error handling
		log.Printf("\n4. Creating TCP listener...")
		network := "tcp"
		log.Printf("4.1 Attempting to bind to %s %s", network, listenAddr)
		
		// Log environment variables that might affect binding
		log.Printf("4.2 Environment variables:")
		for _, env := range os.Environ() {
			if strings.HasPrefix(env, "GODEBUG") || strings.HasPrefix(env, "GOTRACEBACK") || 
			   strings.Contains(env, "PORT") || strings.Contains(env, "ADDR") {
				log.Printf("   %s", env)
			}
		}
		
		// Try to create the listener with a small timeout
		log.Printf("4.3 Creating context with timeout...")
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		
		// Try to bind to the port
		log.Printf("4.4 Creating network listener...")
		var lc net.ListenConfig
		listener, err := lc.Listen(ctx, network, listenAddr)
		log.Printf("4.5 Listener creation result - error: %v", err)
		if err != nil {
			errStr := fmt.Sprintf("4.6 FATAL: Failed to bind to %s %s: %v", network, listenAddr, err)
			log.Printf(errStr)
			log.Printf("4.7 Current time: %s", time.Now().Format(time.RFC3339))
			
			// Detailed error diagnostics
			if opErr, ok := err.(*net.OpError); ok {
				log.Printf("4.2 Network operation error details:")
				log.Printf("   - Operation: %s", opErr.Op)
				log.Printf("   - Network: %s", opErr.Net)
				log.Printf("   - Address: %v", opErr.Addr)
				log.Printf("   - Error: %v", opErr.Err)
				
				if syscallErr, ok := opErr.Err.(*os.SyscallError); ok {
					log.Printf("4.3 System call error:")
					log.Printf("   - Syscall: %s", syscallErr.Syscall)
					log.Printf("   - Error: %v", syscallErr.Err)
					
					// Check for common errors
					switch syscallErr.Err {
					case syscall.EADDRINUSE:
						log.Printf("4.4 ERROR: Port %d is already in use by another process", *port)
					case syscall.EACCES:
						log.Printf("4.4 ERROR: Permission denied - try running with elevated privileges")
					case syscall.EADDRNOTAVAIL:
						log.Printf("4.4 ERROR: Address not available - check network configuration")
					}
				}
			}
			
			errChan <- fmt.Errorf("%s", errStr)
			return
		}

		// If we got here, we successfully bound to the port
		log.Printf("5. Successfully bound to %s", listener.Addr())
		log.Printf("5.1 Listener details: %+v", listener)
		log.Printf("5.2 Starting HTTP server...")
		
		// Log all registered routes
		log.Printf("5.3 Registered routes:")
		// Skip route walking as it's not critical for debugging
		log.Printf("   / - Static file server (frontend)")
		log.Printf("   /ws - WebSocket endpoint")
		
		// Create HTTP server with timeouts
		srv := &http.Server{
			Addr:         listenAddr,
			Handler:      mux,
			ReadTimeout:  30 * time.Second,
			WriteTimeout: 30 * time.Second,
			IdleTimeout:  120 * time.Second,
			ErrorLog:     log.New(os.Stderr, "[HTTP] ", log.LstdFlags),
		}
		
		// Signal that we're ready - port is now bound
		log.Printf("6. Server is ready to accept connections")
		close(readyChan)
		
		// Log startup completion
		log.Printf("7. Starting to serve HTTP requests on %s", listener.Addr())
		
		// Start serving - this will block until the server stops
		log.Printf("7. Starting to serve HTTP requests on %s", listener.Addr())
		err = srv.Serve(listener)
		log.Printf("8. HTTP server stopped: %v", err)
	}()
	
	// Wait for either server ready or error with timeout
	log.Printf("A. Waiting for server to be ready (timeout: 5s)...")
	var serverReady bool
	select {
	case <-readyChan:
		// Server started successfully
		log.Printf("B. SERVER STARTED: Confirmed listening on port %d and ready to accept connections", *port)
		serverReady = true
	case err := <-errChan:
		// Server failed to start
		log.Fatalf("B. FATAL: Failed to start server: %v", err)
	case <-time.After(5 * time.Second):
		// Timeout waiting for server to start
		log.Printf("B. WARNING: Timeout waiting for server to confirm listening, but continuing operation")
		serverReady = true
	}

	if serverReady {
		log.Printf("C. SERVER STATUS: Server is up and running on 0.0.0.0:%d", *port)
		log.Printf("C. ACCESS: You can now access the web terminal at http://<server_ip>:%d", *port)
		log.Printf("C. RUNTIME: Runtime monitoring is %s", map[bool]string{true: "DISABLED (running indefinitely)", false: fmt.Sprintf("ENABLED (will shut down after %d minutes)", *runtimeDuration)}[*noExpiry])
		log.Printf("C. SECURITY: First client IP to connect will be whitelisted")
		log.Printf("C. Use Ctrl+C to stop the server")
		log.Printf("=======================================")
	}
	
	// Wait for interrupt signal
	<-stopChan
	log.Println("Shutting down server...")
	
	// Create a deadline for graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()
	
	// Stop runtime monitor if active
	if runtimeMonitor != nil {
		runtimeMonitor.Stop()
	}
	
	// Attempt graceful shutdown
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}
	
	log.Println("Server exited gracefully")
}

// formatDuration converts a duration to a human-readable format
func formatDuration(d time.Duration) string {
	d = d.Round(time.Minute)
	h := d / time.Hour
	d -= h * time.Hour
	m := d / time.Minute
	
	if h > 0 {
		return fmt.Sprintf("%dh %dm", h, m)
	}
	return fmt.Sprintf("%dm", m)
}

// loadIPWhitelist loads the IP whitelist from a file
func loadIPWhitelist(whitelist *security.IPWhitelist) {
	// Check if whitelist file exists
	if _, err := os.Stat(WhitelistFile); os.IsNotExist(err) {
		log.Println("No whitelist file found. Will create one when first client connects.")
		return
	}

	// Read whitelist file
	data, err := os.ReadFile(WhitelistFile)
	if err != nil {
		log.Printf("Error reading whitelist file: %v", err)
		return
	}

	// Parse whitelist data
	var whitelistData WhitelistData
	if err := json.Unmarshal(data, &whitelistData); err != nil {
		log.Printf("Error parsing whitelist file: %v", err)
		return
	}

	// Add each IP to the whitelist
	for _, ip := range whitelistData.IPs {
		whitelist.AddIP(ip)
	}

	log.Printf("Loaded %d IPs from whitelist file", len(whitelistData.IPs))
}

// saveIPWhitelist saves the IP whitelist to a file
func saveIPWhitelist(whitelist *security.IPWhitelist) {
	// Get list of allowed IPs
	allowedIPs := whitelist.ListAllowedIPs()

	// Create whitelist data
	whitelistData := WhitelistData{
		IPs: allowedIPs,
	}

	// Marshal whitelist data to JSON
	data, err := json.MarshalIndent(whitelistData, "", "  ")
	if err != nil {
		log.Printf("Error marshaling whitelist data: %v", err)
		return
	}

	// Write whitelist data to file
	if err := os.WriteFile(WhitelistFile, data, 0644); err != nil {
		log.Printf("Error writing whitelist file: %v", err)
		return
	}

	log.Printf("Saved %d IPs to whitelist file", len(allowedIPs))
}
