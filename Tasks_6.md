[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:输出一份对frontend前端工程进行整体重构的方案，写入refactory_plan.md（放在工程根目录） DESCRIPTION:重构目标：1- 整体要求：UI框架、风格可完全变更；功能整体保持不变；
2- 具体要求：引入vue或其他流行前端框架，替换现在的UI开发方案
3- 根据所引入的框架，对当前frontend工程进行UI组件化封装，我列举几个：
xterm终端UI组件、AI对话框UI组件（用户输入组件（包含暂停/恢复按钮、发送/终止按钮 及扩展能力）、markdownwn渲染组件、工具调用UI组件、AI流式响应UI组件、完整对话流UI组件 ）AI设置对话框UI组件、虚拟键盘UI组件

-[ ] NAME: 基于refactory_plan.md为重构指导，开始重构 DESCRIPTION:frontend 目录备份为 frontend-old，新建frontend放置重构工程,从0开启新工程，逐步实现UI组件，组装各功能模块，整体编译打包测试
