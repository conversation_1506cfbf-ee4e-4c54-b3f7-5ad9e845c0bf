import React from 'react';
import { Layout, Typography } from 'antd';
import styled from 'styled-components';

import { UI_CONSTANTS } from '@/constants';

const { Footer: AntFooter } = Layout;
const { Text } = Typography;

const StyledFooter = styled(AntFooter)`
  background: #1e1e1e;
  border-top: 1px solid #444;
  text-align: center;
  padding: 12px 24px;
  height: ${UI_CONSTANTS.FOOTER_HEIGHT}px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const FooterText = styled(Text)`
  color: #888 !important;
  font-size: 12px;
`;

const Footer: React.FC = () => {
  return (
    <StyledFooter>
      <FooterText>
        Powered by xterm.js and Go WebSocket
      </FooterText>
    </StyledFooter>
  );
};

export default Footer;
