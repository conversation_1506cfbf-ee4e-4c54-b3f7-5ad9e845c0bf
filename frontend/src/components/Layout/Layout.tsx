import React from 'react';
import { Layout as AntLayout } from 'antd';
import styled from 'styled-components';

import Header from './Header';
import Footer from './Footer';
import { UI_CONSTANTS } from '@/constants';

const { Content } = AntLayout;

const StyledLayout = styled(AntLayout)`
  min-height: 100vh;
  background: #1e1e1e;
`;

const StyledContent = styled(Content)`
  padding: 16px;
  margin: 0;
  background: #1e1e1e;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - ${UI_CONSTANTS.HEADER_HEIGHT + UI_CONSTANTS.FOOTER_HEIGHT}px);
`;

interface LayoutProps {
  children: React.ReactNode;
  onGetAllContent?: () => void;
  onToggleKeyboard?: () => void;
  onOpenAI?: () => void;
  onOpenSettings?: () => void;
}

const Layout: React.FC<LayoutProps> = ({
  children,
  onGetAllContent,
  onToggleKeyboard,
  onOpenAI,
  onOpenSettings
}) => {
  return (
    <StyledLayout>
      <Header
        onGetAllContent={onGetAllContent}
        onToggleKeyboard={onToggleKeyboard}
        onOpenAI={onOpenAI}
        onOpenSettings={onOpenSettings}
      />
      <StyledContent>
        {children}
      </StyledContent>
      <Footer />
    </StyledLayout>
  );
};

export default Layout;
