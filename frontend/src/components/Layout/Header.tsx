import React from 'react';
import { Layout, Typography, Button, Space } from 'antd';
import {
  DesktopOutlined,
  KeyOutlined,
  RobotOutlined,
  SettingOutlined
} from '@ant-design/icons';
import styled from 'styled-components';

import StatusBar from '../StatusBar/StatusBar';
import { useKeyboardVisibility, useToggle } from '@/stores/keyboardStore';
import { UI_CONSTANTS } from '@/constants';

const { Header: AntHeader } = Layout;
const { Title } = Typography;

const StyledHeader = styled(AntHeader)`
  background: #1e1e1e;
  border-bottom: 1px solid #444;
  padding: 0 24px;
  height: ${UI_CONSTANTS.HEADER_HEIGHT}px;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const HeaderContent = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
`;

const TitleSection = styled.div`
  display: flex;
  align-items: center;
`;

const StyledTitle = styled(Title)`
  color: #f0f0f0 !important;
  margin: 0 !important;
  font-size: 24px !important;
`;

const ActionButtons = styled(Space)`
  .ant-btn {
    background: transparent;
    border: 1px solid #444;
    color: #f0f0f0;

    &:hover {
      background: #333;
      border-color: #666;
      color: #fff;
    }

    &:focus {
      background: #333;
      border-color: #666;
      color: #fff;
    }
  }
`;

interface HeaderProps {
  onGetAllContent?: () => void;
  onToggleKeyboard?: () => void;
  onOpenAI?: () => void;
  onOpenSettings?: () => void;
}

const Header: React.FC<HeaderProps> = ({
  onGetAllContent,
  onToggleKeyboard,
  onOpenAI,
  onOpenSettings,
}) => {
  const keyboardVisible = useKeyboardVisibility();
  const toggleKeyboard = useToggle();

  const handleGetAllContent = () => {
    if (onGetAllContent) {
      onGetAllContent();
    }
  };

  const handleToggleKeyboard = () => {
    if (onToggleKeyboard) {
      onToggleKeyboard();
    } else {
      toggleKeyboard();
    }
  };

  const handleOpenAI = () => {
    if (onOpenAI) {
      onOpenAI();
    }
  };

  const handleOpenSettings = () => {
    if (onOpenSettings) {
      onOpenSettings();
    }
  };

  return (
    <StyledHeader>
      <HeaderContent>
        <TitleSection>
          <StyledTitle level={3}>Web Terminal</StyledTitle>
        </TitleSection>

        <ActionButtons size="middle">
          <Button
            icon={<DesktopOutlined />}
            title="获取全屏内容"
            onClick={handleGetAllContent}
          />
          <Button
            icon={<KeyOutlined />}
            title="Toggle Virtual Keyboard"
            onClick={handleToggleKeyboard}
            type={keyboardVisible ? 'primary' : 'default'}
          />
          <Button
            icon={<RobotOutlined />}
            title="Open AI Chat"
            onClick={handleOpenAI}
          />
          <Button
            icon={<SettingOutlined />}
            title="AI Settings"
            onClick={handleOpenSettings}
          />
        </ActionButtons>
      </HeaderContent>

      <StatusBar />
    </StyledHeader>
  );
};

export default Header;
