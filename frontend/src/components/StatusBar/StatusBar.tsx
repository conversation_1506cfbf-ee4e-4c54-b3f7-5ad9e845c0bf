import React, { useState, useEffect } from 'react';
import { Space, Button, Typography, Badge } from 'antd';
import { ClockCircleOutlined, PlusOutlined } from '@ant-design/icons';
import styled from 'styled-components';

import type { StatusBarProps, SessionInfo } from '@/types';
import { SESSION_CONFIG, API_ENDPOINTS } from '@/constants';

const { Text } = Typography;

const StatusContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  margin-left: 24px;
  width: 200px;
`;

const StatusText = styled(Text)<{ $status: 'normal' | 'warning' | 'danger' }>`
  color: ${props => {
    switch (props.$status) {
      case 'warning': return '#faad14 !important';
      case 'danger': return '#ff4d4f !important';
      default: return '#52c41a !important';
    }
  }};
  font-weight: 500;
`;

const ExtendButton = styled(Button)`
  background: #52c41a;
  border-color: #52c41a;
  color: #fff;
  
  &:hover {
    background: #73d13d;
    border-color: #73d13d;
    color: #fff;
  }
  
  &:focus {
    background: #73d13d;
    border-color: #73d13d;
    color: #fff;
  }
  
  &:disabled {
    background: #666;
    border-color: #666;
    color: #999;
  }
`;

const StatusBar: React.FC<Partial<StatusBarProps>> = ({ 
  sessionInfo: propSessionInfo,
  onExtend 
}) => {
  const [sessionInfo, setSessionInfo] = useState<SessionInfo>({
    remainingTime: SESSION_CONFIG.DEFAULT_DURATION,
    isActive: true,
    canExtend: true,
  });
  const [isExtending, setIsExtending] = useState(false);

  // Use prop sessionInfo if provided, otherwise use local state
  const currentSessionInfo = propSessionInfo || sessionInfo;

  useEffect(() => {
    // Initialize session timer
    const checkSessionStatus = async () => {
      try {
        const response = await fetch(API_ENDPOINTS.SESSION_STATUS);
        if (response.ok) {
          const data = await response.json();
          setSessionInfo({
            remainingTime: data.remaining_seconds || SESSION_CONFIG.DEFAULT_DURATION,
            isActive: true,
            canExtend: true,
          });
        }
      } catch (error) {
        console.warn('Failed to fetch session status:', error);
      }
    };

    // Check status immediately
    checkSessionStatus();

    // Set up timer to update remaining time
    const timer = setInterval(() => {
      setSessionInfo(prev => ({
        ...prev,
        remainingTime: Math.max(0, prev.remainingTime - 1),
      }));
    }, SESSION_CONFIG.UPDATE_INTERVAL);

    // Check status periodically
    const statusTimer = setInterval(checkSessionStatus, 30000); // Every 30 seconds

    return () => {
      clearInterval(timer);
      clearInterval(statusTimer);
    };
  }, []);

  const formatTime = (seconds: number): string => {
    // console.log(`[formatTime] Input: ${seconds} seconds`);
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    let result: string;
    if (hours > 0) {
      result = `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
      result = `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
    
    // console.log(`[formatTime] Output: ${result}`);
    return result;
  };

  const getStatusType = (remainingTime: number): 'normal' | 'warning' | 'danger' => {
    if (remainingTime <= 60) return 'danger'; // Less than 1 minute
    if (remainingTime <= SESSION_CONFIG.WARNING_THRESHOLD) return 'warning'; // Less than 5 minutes
    return 'normal';
  };

  const handleExtendSession = async () => {
    if (isExtending || !currentSessionInfo.canExtend) return;

    setIsExtending(true);
    
    try {
      const response = await fetch(API_ENDPOINTS.SESSION_EXTEND, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        setSessionInfo(prev => ({
          ...prev,
          remainingTime: prev.remainingTime + SESSION_CONFIG.EXTEND_DURATION,
        }));
        
        onExtend?.();
      } else {
        console.error('Failed to extend session');
      }
    } catch (error) {
      console.error('Error extending session:', error);
    } finally {
      setIsExtending(false);
    }
  };

  const statusType = getStatusType(currentSessionInfo.remainingTime);
  const showBadge = statusType === 'warning' || statusType === 'danger';

  return (
    <StatusContainer>
      <Space align="center">
        <ClockCircleOutlined style={{ color: '#f0f0f0' }} />
        <StatusText $status={statusType}>
          {currentSessionInfo.isActive 
            ? formatTime(currentSessionInfo.remainingTime)
            : 'Session Inactive'
          }
        </StatusText>
      </Space>
      
      {currentSessionInfo.isActive && currentSessionInfo.canExtend && (
        <Badge dot={showBadge} color={statusType === 'danger' ? 'red' : 'orange'}>
          <ExtendButton
            size="small"
            icon={<PlusOutlined />}
            loading={isExtending}
            disabled={!currentSessionInfo.canExtend}
            onClick={handleExtendSession}
            title="Extend session by 1 hour"
          >
            1小时
          </ExtendButton>
        </Badge>
      )}
    </StatusContainer>
  );
};

export default StatusBar;
