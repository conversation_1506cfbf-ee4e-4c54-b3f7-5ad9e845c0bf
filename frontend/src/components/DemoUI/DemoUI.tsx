import React from 'react';

interface DemoUIProps {
  open: boolean;
  onClose: () => void;
}

let oldFun: () => void;

const DemoUI: React.FC<DemoUIProps> = ({ open, onClose }) => {
  console.log('%c[DemoUI] Component Render', 'color: #FFA500;', { open });

//   if (!open) {
//     return null;
//   }
  if(oldFun === onClose) {
    console.log('%c[DemoUI] function not changed', 'color: #FFA500;', { onClose });
  } else {
    console.log('%c[DemoUI] function changed', 'color: #FFA500;', { onClose });
    oldFun = onClose;
  }

  return (
    <div style={{
      position: 'fixed',
      top: '20%',
      left: '50%',
      transform: 'translateX(-50%)',
      padding: '20px',
      background: '#333',
      border: '1px solid #555',
      borderRadius: '8px',
      zIndex: 1050, // Higher than other elements
      color: '#f0f0f0',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.5)'
    }}>
      <h2 style={{ marginTop: 0 }}>Demo UI Component</h2>
      <p>This is a simple component for debugging render loops.</p>
      <button 
        onClick={oldFun} 
        style={{
          padding: '8px 16px',
          border: 'none',
          background: '#52c41a',
          color: 'white',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        Close
      </button>
    </div>
  );
};

export default DemoUI;
