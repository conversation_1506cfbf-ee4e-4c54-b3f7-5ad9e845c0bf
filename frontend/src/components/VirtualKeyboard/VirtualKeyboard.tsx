import React, { useCallback } from 'react';
import { createPortal } from 'react-dom';
import { Button, Space } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import styled from 'styled-components';

import {
  useKeyboardVisibility,
  useKeyboardLayout,
  useSetShiftPressed,
  useSetCtrlPressed,
  useSetAltPressed,
  useResetModifiers,
  useSetVisible,
  useSetKeyboardLayout,
  useKeyboardLayouts,
  useShiftPressed,
  useCtrlPressed,
  useAltPressed,
} from '@/stores/keyboardStore';
import { xtermService } from '@/services/xtermService';
import type { VirtualKeyboardProps, KeyboardKey } from '@/types';
import { Z_INDEX, ANIMATION_DURATION } from '@/constants';

const KeyboardContainer = styled.div<{ $visible: boolean }>`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #1e1e1e;
  border-top: 1px solid #444;
  box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.7);
  z-index: ${Z_INDEX.MODAL};
  transition: transform ${ANIMATION_DURATION.NORMAL}ms ease-in-out;
  transform: translateY(${props => props.$visible ? '0' : '100%'});
  max-height: 50vh;
  overflow: hidden;
`;

const KeyboardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #2d2d2d;
  border-bottom: 1px solid #444;
`;

const KeyboardControls = styled(Space)`
  .ant-btn {
    background: #333;
    color: #ccc;
    border: 1px solid #555;
    
    &:hover {
      background: #444;
      border-color: #666;
      color: #fff;
    }
    
    &.ant-btn-primary {
      background: #52c41a;
      border-color: #52c41a;
      color: #fff;
      
      &:hover {
        background: #73d13d;
        border-color: #73d13d;
      }
    }
  }
`;

const CloseButton = styled(Button)`
  background: none !important;
  border: none !important;
  color: #ccc !important;
  font-size: 18px;
  
  &:hover {
    background: #444 !important;
    color: #fff !important;
  }
`;

const KeyboardBody = styled.div`
  padding: 12px;
  overflow-y: auto;
  max-height: calc(50vh - 60px);
`;

const KeyboardKeys = styled.div`
  display: flex;
  flex-direction: column;
  gap: 6px;
  max-width: 100%;
`;

const KeyboardRow = styled.div`
  display: flex;
  justify-content: center;
  gap: 4px;
  flex-wrap: nowrap;
`;

const KeyboardKeyButton = styled(Button)<{ 
  $width?: number; 
  $active?: boolean;
  $modifier?: boolean;
}>`
  min-width: ${props => props.$width ? `${props.$width * 40}px` : '40px'};
  min-height: 40px;
  padding: 8px 4px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: ${props => {
    if (props.$active) return '#52c41a';
    if (props.$modifier) return '#444';
    return '#333';
  }} !important;
  border-color: ${props => {
    if (props.$active) return '#52c41a';
    if (props.$modifier) return '#666';
    return '#555';
  }} !important;
  color: ${props => props.$active ? '#fff' : '#ccc'} !important;
  
  &:hover {
    background: ${props => {
      if (props.$active) return '#73d13d';
      return '#444';
    }} !important;
    border-color: ${props => {
      if (props.$active) return '#73d13d';
      return '#666';
    }} !important;
    color: #fff !important;
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  @media (max-width: 768px) {
    min-width: ${props => props.$width ? `${props.$width * 32}px` : '32px'};
    min-height: 32px;
    font-size: 11px;
    padding: 4px 2px;
  }
  
  @media (max-width: 480px) {
    min-width: ${props => props.$width ? `${props.$width * 28}px` : '28px'};
    min-height: 28px;
    font-size: 10px;
    padding: 4px;
  }
`;

const VirtualKeyboard: React.FC<VirtualKeyboardProps> = ({
  visible: propVisible,
  onKeyPress: propOnKeyPress,
  onToggle: propOnToggle,
}) => {
  const isVisible = useKeyboardVisibility();
  const currentLayout = useKeyboardLayout();
  const shiftPressed = useShiftPressed();
  const ctrlPressed = useCtrlPressed();
  const altPressed = useAltPressed();
  const setVisible = useSetVisible();
  const setLayout = useSetKeyboardLayout();
  const setShiftPressed = useSetShiftPressed();
  const setCtrlPressed = useSetCtrlPressed();
  const setAltPressed = useSetAltPressed();
  const resetModifiers = useResetModifiers();
  const layouts = useKeyboardLayouts();

  // Use prop visibility if provided, otherwise use store state
  const visible = propVisible !== undefined ? propVisible : isVisible;

  // Handle key press
  const handleKeyPress = useCallback((keyData: KeyboardKey) => {
    console.log('Virtual keyboard key pressed:', keyData);

    // Handle modifier keys
    if (keyData.key === 'Shift') {
      setShiftPressed(!shiftPressed);
      return;
    }

    if (keyData.key === 'Ctrl') {
      setCtrlPressed(!ctrlPressed);
      return;
    }

    if (keyData.key === 'Alt') {
      setAltPressed(!altPressed);
      return;
    }

    if (keyData.key === 'CapsLock') {
      setShiftPressed(!shiftPressed);
      return;
    }

    // Build key data with current modifiers
    const finalKeyData: KeyboardKey = {
      ...keyData,
      shift: shiftPressed,
      ctrl: ctrlPressed,
      alt: altPressed,
    };

    // Call external callback if provided
    if (propOnKeyPress) {
      propOnKeyPress(finalKeyData);
    } else {
      // Default behavior: send to terminal
      sendKeyToTerminal(finalKeyData);
    }

    // Reset shift after use (but not caps lock)
    if (shiftPressed && keyData.key !== 'CapsLock') {
      setShiftPressed(false);
    }

    // Reset other modifiers after use
    if (ctrlPressed || altPressed) {
      resetModifiers();
    }
  }, [
    shiftPressed,
    ctrlPressed,
    altPressed,
    propOnKeyPress,
    setShiftPressed,
    setCtrlPressed,
    setAltPressed,
    resetModifiers,
  ]);

  // Send key to terminal
  const sendKeyToTerminal = useCallback((keyData: KeyboardKey) => {
    if (!xtermService.isServiceReady()) {
      console.warn('Terminal service not ready');
      return;
    }

    const { key, shift, ctrl, alt } = keyData;

    // Handle special key combinations
    if (ctrl && alt) {
      xtermService.sendKeyCombination(`Ctrl+Alt+${key}`);
    } else if (ctrl) {
      xtermService.sendKeyCombination(`Ctrl+${key}`);
    } else if (alt) {
      xtermService.sendKeyCombination(`Alt+${key}`);
    } else if (shift && key.length === 1) {
      // For single characters with shift, send the shifted character
      xtermService.sendKey(key.toUpperCase());
    } else {
      xtermService.sendKey(key);
    }
  }, []);

  // Handle layout switch
  const handleLayoutSwitch = useCallback((layout: string) => {
    setLayout(layout);
  }, [setLayout]);

  // Handle close
  const handleClose = useCallback(() => {
    if (propOnToggle) {
      propOnToggle();
    } else {
      setVisible(false);
    }
  }, [propOnToggle, setVisible]);

  // Get current layout data
  const currentLayoutData = shiftPressed && layouts.shift 
    ? layouts.shift 
    : layouts[currentLayout];

  // Render keyboard key
  const renderKey = useCallback((keyData: KeyboardKey, index: number) => {
    const isModifier = ['Shift', 'Ctrl', 'Alt', 'CapsLock'].includes(keyData.key);
    const isActive = (
      (keyData.key === 'Shift' && shiftPressed) ||
      (keyData.key === 'Ctrl' && ctrlPressed) ||
      (keyData.key === 'Alt' && altPressed) ||
      (keyData.key === 'CapsLock' && shiftPressed)
    );

    return (
      <KeyboardKeyButton
        key={`${keyData.key}-${index}`}
        $width={keyData.width}
        $active={isActive}
        $modifier={isModifier}
        onClick={() => handleKeyPress(keyData)}
        size="small"
      >
        {keyData.label || keyData.key}
      </KeyboardKeyButton>
    );
  }, [shiftPressed, ctrlPressed, altPressed, handleKeyPress]);

  // Create portal to render keyboard at document root
  const keyboardElement = (
    <KeyboardContainer $visible={visible}>
      <KeyboardHeader>
        <KeyboardControls size="small">
          <Button
            type={currentLayout === 'default' ? 'primary' : 'default'}
            size="small"
            onClick={() => handleLayoutSwitch('default')}
          >
            ABC
          </Button>
          <Button
            type={currentLayout === 'function' ? 'primary' : 'default'}
            size="small"
            onClick={() => handleLayoutSwitch('function')}
          >
            F1-12
          </Button>
        </KeyboardControls>
        <CloseButton
          type="text"
          icon={<CloseOutlined />}
          onClick={handleClose}
          size="small"
        />
      </KeyboardHeader>
      <KeyboardBody>
        <KeyboardKeys>
          {currentLayoutData?.rows.map((row, rowIndex) => (
            <KeyboardRow key={rowIndex}>
              {row.map((keyData, keyIndex) => renderKey(keyData, keyIndex))}
            </KeyboardRow>
          ))}
        </KeyboardKeys>
      </KeyboardBody>
    </KeyboardContainer>
  );

  return createPortal(keyboardElement, document.body);
};

export default VirtualKeyboard;
