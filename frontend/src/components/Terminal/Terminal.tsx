import React, { useEffect, useMemo, useRef } from 'react';
import { Terminal as XTerm, type IDisposable } from '@xterm/xterm';
import { FitAddon } from '@xterm/addon-fit';
import { WebLinksAddon } from '@xterm/addon-web-links';
import '@xterm/xterm/css/xterm.css';
import styled from 'styled-components';

import { useTerminalStore } from '@/stores/terminalStore';
import type { TerminalProps } from '@/types';
import { useReceived, useSend, useIsConnected } from '@/stores/websocketStore';

const TerminalContainer = styled.div`
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 4px;
  overflow: hidden;
  box-sizing: border-box;
  /* Ensure it fills the parent */
  min-height: 700px;

  .xterm {
    width: 100%;
    height: 100%;
  }

  .xterm-viewport {
    background: transparent !important;
    overflow: hidden !important; 
  }

  .xterm-screen {
    background: transparent !important;
  }
`;

const Terminal: React.FC<TerminalProps> = ({ className, onReady }) => {
  const terminalRef = useRef<HTMLDivElement>(null);
  const terminalInstanceRef = useRef<XTerm | null>(null);
  const fitAddonRef = useRef<FitAddon | null>(null);
  const terminalOnDataFunc = useRef<IDisposable | null>(null);
  const terminalResizeFunc = useRef<IDisposable | null>(null);
  const initd = useRef<boolean>(false);
  // const mountedRef = useRef(true);

  const config = useTerminalStore((state) => state.config);
  const setTerminal = useTerminalStore((state) => state.setTerminal);
  const setFitAddon = useTerminalStore((state) => state.setFitAddon);
  const setReady = useTerminalStore((state) => state.setReady);
  const addToBuffer = useTerminalStore((state) => state.addToBuffer);
  const send = useSend();
  const isConnected = useIsConnected();
  const received = useReceived();
  const memoizedOnReady = useMemo(() => onReady, [onReady]);

  useEffect(() => {
    if (!terminalRef.current /* || !mountedRef.current */) return;

    const container = terminalRef.current;
    if (!container) return;

    console.log('Initializing terminal...');
    console.log('Terminal container dimensions:', {
      width: container.offsetWidth,
      height: container.offsetHeight,
      clientWidth: container.clientWidth,
      clientHeight: container.clientHeight
    });

    // Check if container has valid dimensions
    if (container.offsetWidth === 0 || container.offsetHeight === 0) {
      console.warn('Terminal container has no dimensions, will proceed anyway...');
      // Don't retry, just proceed with initialization
      // The CSS should ensure the container has dimensions
    }

    console.log('Creating terminal instance...');
    const terminal = new XTerm({
      fontFamily: config.fontFamily,
      fontSize: config.fontSize,
      lineHeight: config.lineHeight,
      cursorBlink: config.cursorBlink,
      cursorStyle: config.cursorStyle,
      theme: config.theme,
      allowProposedApi: true,
    });
    console.log('Creating addons...');
    const fitAddon = new FitAddon();
    const webLinksAddon = new WebLinksAddon();
    terminal.loadAddon(fitAddon);
    terminal.loadAddon(webLinksAddon);

    console.log('Opening terminal in container...');
    terminal.open(container);
    console.log('Fitting terminal...');
    setTimeout(() => {
      // if (mountedRef.current) {
        try {
          fitAddon.fit();
          console.log('Terminal fitted successfully');
        } catch (error) {
          console.warn('Failed to fit terminal:', error);
        }
      // }
    }, 100);

    console.log('Setting up terminal references...');
    terminalInstanceRef.current = terminal;
    fitAddonRef.current = fitAddon;
    setTerminal(terminal);
    setFitAddon(fitAddon);
    setReady(true);
    memoizedOnReady?.();

    console.log('[Terminal] Terminal is ready');

    terminal.writeln('Welcome to Web Terminal!');
    // terminal.write('$ ');
    const handleResize = () => {
      window.setTimeout(() => {
        if (fitAddon /* && mountedRef.current */) {
          try {
            fitAddon.fit();
          } catch {
            console.warn('Failed to fit terminal after resize');
          }
        }
      }, 0);
    };
    window.addEventListener('resize', handleResize);
    return () => {
      console.log('[Terminal] Terminal unmounting...');
      // mountedRef.current = false;
      window.removeEventListener('resize', handleResize);
      setReady(false);
      try {
        terminal.dispose();
      } catch (error) {
        console.warn('Error disposing terminal:', error);
      }
    };
  }, []); // 依赖项为空数组，只在挂载时执行

  useEffect(() => {
    const terminal = terminalInstanceRef.current;
    if (!terminal /* || !mountedRef.current */) return;

    try {
      terminal.options.fontFamily = config.fontFamily;
      terminal.options.fontSize = config.fontSize;
      terminal.options.lineHeight = config.lineHeight;
      terminal.options.cursorBlink = config.cursorBlink;
      terminal.options.cursorStyle = config.cursorStyle;
      terminal.options.theme = config.theme;

      // Refresh terminal display
      terminal.refresh(0, terminal.rows - 1);

      // Refit terminal
      if (fitAddonRef.current /* && mountedRef.current */) {
        window.setTimeout(() => {
          if (/* mountedRef.current && */ fitAddonRef.current) {
            try {
              fitAddonRef.current.fit();
            } catch (error) {
              console.warn('Failed to fit terminal after config update:', error);
            }
          }
        }, 0);
      }
    } catch (error) {
      console.warn('Failed to update terminal options:', error);
    }
  }, [config]);

  // Handle WebSocket connection changes
  useEffect(() => {
    const terminal = terminalInstanceRef.current;

    // if (!mountedRef.current) return;
    if(terminalOnDataFunc.current) {
      terminalOnDataFunc.current.dispose();
    }
    if(terminalResizeFunc.current) {
      terminalResizeFunc.current.dispose();
    }
    if (isConnected && terminal) {
      initd.current = true;
      console.log('WebSocket connected, initializing XTerm service...');
      // Initialize XTerm service
      // xtermService.initialize(terminal, socket!);
      // if (mountedRef.current) {
      setReady(true);
      memoizedOnReady?.();
      // }

      try {
        terminal.writeln('\r\nConnected to terminal server');
        terminal.write('$ ');
      } catch (error) {
        console.warn('Failed to write to terminal:', error);
      }

      terminalOnDataFunc.current = terminal.onData((data) => {
        console.log('[Terminal] Terminal input:', data, 'char codes:', data.split('').map(c => c.charCodeAt(0)));
        addToBuffer(data);
        const message = JSON.stringify({
          type: 'input',
          data: data
        });
        console.log('[Terminal] Sending to WebSocket:', message);
        send(message);
      });
      terminalResizeFunc.current = terminal.onResize(({ cols, rows }) => {
        console.log('[Terminal] Terminal resized:', { cols, rows });
        send(JSON.stringify({
          type: 'resize',
          data: JSON.stringify({ cols, rows })
        }));
      });
      send(JSON.stringify({
        type: 'resize',
        data: JSON.stringify({ cols: terminal.cols, rows: terminal.rows })
      }));
    } else if (!isConnected && terminal) {
      if(!initd.current) {
        return;
      }
      console.log('WebSocket disconnected');
      // if (mountedRef.current) {
        setReady(false);
      // }
      try {
        terminal.writeln('\r\nDisconnected from terminal server');
        terminal.write('$ ');
      } catch (error) {
        console.warn('Failed to write to terminal:', error);
      }
      
      terminalOnDataFunc.current = terminal.onData((data) => {
        console.log('[Terminal] Terminal input:', data, 'char codes:', data.split('').map(c => c.charCodeAt(0)));
        addToBuffer(data);
        console.log('[Terminal] WebSocket not connected, echoing locally. Socket:', 'Connected:', isConnected);
      //   // Fallback: echo locally if not connected
        terminal.write(data);
      });      
    }
  }, [isConnected, setReady, memoizedOnReady, addToBuffer, send]);

  // Handle WebSocket messages
  useEffect(() => {
    // if (!mountedRef.current) return;
    if(!received) return;

    console.log('[Terminal] Received WebSocket message:', received!.data);
    try {
      const message = JSON.parse(received!.data);
      console.log('[Terminal] Parsed message:', message);
      const terminal = terminalInstanceRef.current;

      if (terminal && message.type === 'output') {
        console.log('[Terminal] Writing output to terminal:', message.data);
        terminal.write(message.data);
      } else {
        console.log('[Terminal] Message not handled - type:', message.type, 'terminal:', !!terminal);
      }
    } catch (error) {
      console.error('[Terminal] Failed to parse WebSocket message:', error, 'Raw data:', received!.data);
      // Try to write raw data to terminal as fallback
      const terminal = terminalInstanceRef.current;
      if (terminal) {
        terminal.write(received!.data);
      }
    }
    
  }, [received]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // mountedRef.current = false;
    };
  }, []);

  return (
    <TerminalContainer className={className}>
      <div ref={terminalRef} style={{ width: '100%', height: '100%' }}/>
    </TerminalContainer>
  );
};

export default Terminal;
