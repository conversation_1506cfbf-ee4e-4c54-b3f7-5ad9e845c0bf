import { Component } from 'react';
import type { ErrorInfo, ReactNode } from 'react';
import { Result, Button } from 'antd';
import styled from 'styled-components';

const ErrorContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #1e1e1e;
  color: #f0f0f0;
  
  .ant-result {
    background: transparent;
    
    .ant-result-title {
      color: #f0f0f0;
    }
    
    .ant-result-subtitle {
      color: #ccc;
    }
  }
  
  .ant-btn {
    background: #52c41a;
    border-color: #52c41a;
    color: #fff;
    
    &:hover {
      background: #73d13d;
      border-color: #73d13d;
    }
  }
`;

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  handleReload = () => {
    window.location.reload();
  };

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      return (
        <ErrorContainer>
          <Result
            status="error"
            title="Something went wrong"
            subTitle="The application encountered an unexpected error. Please try reloading the page."
            extra={[
              <Button key="reload" type="primary" onClick={this.handleReload}>
                Reload Page
              </Button>,
              <Button key="reset" onClick={this.handleReset}>
                Try Again
              </Button>,
            ]}
          >
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <div style={{ 
                textAlign: 'left', 
                background: '#2d2d2d', 
                padding: '16px', 
                borderRadius: '4px',
                marginTop: '16px',
                fontSize: '12px',
                fontFamily: 'monospace',
                color: '#ff6b6b'
              }}>
                <div><strong>Error:</strong> {this.state.error.message}</div>
                {this.state.errorInfo && (
                  <div style={{ marginTop: '8px' }}>
                    <strong>Stack:</strong>
                    <pre style={{ whiteSpace: 'pre-wrap', margin: '4px 0' }}>
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </div>
                )}
              </div>
            )}
          </Result>
        </ErrorContainer>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
