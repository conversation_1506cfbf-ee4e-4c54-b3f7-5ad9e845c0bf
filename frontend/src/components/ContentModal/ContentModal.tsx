import React from 'react';
import { Mo<PERSON>, Typo<PERSON>, Button, Space, message } from 'antd';
import { CopyOutlined, DownloadOutlined } from '@ant-design/icons';
import styled from 'styled-components';

const { Text } = Typography;

const ContentContainer = styled.div`
  background: #1e1e1e;
  border: 1px solid #444;
  border-radius: 4px;
  padding: 16px;
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Menlo, Monaco, "Courier New", monospace';
  font-size: 12px;
  line-height: 1.4;
  color: #f0f0f0;
  white-space: pre-wrap;
  word-break: break-all;
`;

const StyledModal = styled(Modal)`
  .ant-modal-content {
    background: #2d2d2d;
  }
  
  .ant-modal-header {
    background: #2d2d2d;
    border-bottom: 1px solid #444;
    
    .ant-modal-title {
      color: #f0f0f0;
    }
  }
  
  .ant-modal-body {
    background: #2d2d2d;
    padding: 16px;
  }
  
  .ant-modal-footer {
    background: #2d2d2d;
    border-top: 1px solid #444;
    
    .ant-btn {
      background: #333;
      border-color: #555;
      color: #f0f0f0;
      
      &:hover {
        background: #444;
        border-color: #666;
        color: #fff;
      }
      
      &.ant-btn-primary {
        background: #52c41a;
        border-color: #52c41a;
        
        &:hover {
          background: #73d13d;
          border-color: #73d13d;
        }
      }
    }
  }
`;

interface ContentModalProps {
  open: boolean;
  content: string;
  title?: string;
  onClose: () => void;
}

const ContentModal: React.FC<ContentModalProps> = ({
  open,
  content,
  title = 'Terminal Content',
  onClose,
}) => {
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(content);
      message.success('Content copied to clipboard');
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      message.error('Failed to copy to clipboard');
    }
  };

  const handleDownload = () => {
    try {
      const blob = new Blob([content], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `terminal-content-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      message.success('Content downloaded');
    } catch (error) {
      console.error('Failed to download content:', error);
      message.error('Failed to download content');
    }
  };

  return (
    <StyledModal
      title={title}
      open={open}
      onCancel={onClose}
      width={800}
      footer={[
        <Button key="close" onClick={onClose}>
          Close
        </Button>,
        <Button key="copy" icon={<CopyOutlined />} onClick={handleCopy}>
          Copy
        </Button>,
        <Button key="download" type="primary" icon={<DownloadOutlined />} onClick={handleDownload}>
          Download
        </Button>,
      ]}
    >
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        <Text type="secondary">
          Terminal content ({content.length} characters)
        </Text>
        <ContentContainer>
          {content || 'No content available'}
        </ContentContainer>
      </Space>
    </StyledModal>
  );
};

export default ContentModal;
