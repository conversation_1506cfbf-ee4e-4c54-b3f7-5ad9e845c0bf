import React, { useState } from 'react';
import { Modal, Form, Input, Select, Slider, Switch, Button, Typography, Divider, message } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import styled from 'styled-components';

import { useTerminalConfig, useTerminalActions } from '@/stores/terminalStore';
import { useKeyboardLayout, useSetKeyboardLayout } from '@/stores/keyboardStore';
import { TERMINAL_THEME, KEYBOARD_LAYOUTS } from '@/constants';

const { Title, Text } = Typography;
const { Option } = Select;

const StyledModal = styled(Modal)`
  .ant-modal-content {
    background: #1e1e1e;
  }
  
  .ant-modal-header {
    background: #1e1e1e;
    border-bottom: 1px solid #444;
    
    .ant-modal-title {
      color: #f0f0f0;
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  
  .ant-modal-body {
    background: #1e1e1e;
    padding: 24px;
  }
  
  .ant-modal-footer {
    background: #1e1e1e;
    border-top: 1px solid #444;
    
    .ant-btn {
      background: #333;
      border-color: #555;
      color: #f0f0f0;
      
      &:hover {
        background: #444;
        border-color: #666;
        color: #fff;
      }
      
      &.ant-btn-primary {
        background: #52c41a;
        border-color: #52c41a;
        
        &:hover {
          background: #73d13d;
          border-color: #73d13d;
        }
      }
    }
  }
`;

const StyledForm = styled(Form)`
  .ant-form-item-label > label {
    color: #f0f0f0 !important;
  }
  
  .ant-input {
    background: #2d2d2d !important;
    border-color: #555 !important;
    color: #f0f0f0 !important;
    
    &:focus {
      border-color: #52c41a !important;
      box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2) !important;
    }
  }
  
  .ant-select {
    .ant-select-selector {
      background: #2d2d2d !important;
      border-color: #555 !important;
      color: #f0f0f0 !important;
    }
    
    &.ant-select-focused .ant-select-selector {
      border-color: #52c41a !important;
      box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2) !important;
    }
  }
  
  .ant-slider {
    .ant-slider-rail {
      background: #444 !important;
    }
    
    .ant-slider-track {
      background: #52c41a !important;
    }
    
    .ant-slider-handle {
      border-color: #52c41a !important;
      
      &:focus {
        box-shadow: 0 0 0 5px rgba(82, 196, 26, 0.2) !important;
      }
    }
  }
  
  .ant-switch {
    &.ant-switch-checked {
      background: #52c41a !important;
    }
  }
`;

const SectionTitle = styled(Title)`
  color: #f0f0f0 !important;
  font-size: 16px !important;
  margin-bottom: 16px !important;
`;

interface SettingsModalProps {
  open: boolean;
  onClose: () => void;
}

const SettingsModal: React.FC<SettingsModalProps> = ({ open, onClose }) => {
  console.log('SettingsModal open', open);
  const terminalConfig = useTerminalConfig();
  const updateTerminalConfig = useTerminalActions();
  const currentLayout = useKeyboardLayout();
  const setKeyboardLayout = useSetKeyboardLayout();
  
  const [form] = Form.useForm();
  const [hasChanges, setHasChanges] = useState(false);

  // Initialize form values
  const initialValues = {
      fontFamily: terminalConfig.fontFamily,
      fontSize: terminalConfig.fontSize,
      lineHeight: terminalConfig.lineHeight,
      cursorBlink: terminalConfig.cursorBlink,
      cursorStyle: terminalConfig.cursorStyle,
    theme: terminalConfig.theme === TERMINAL_THEME.DARK ? 'dark' : 'light',
    keyboardLayout: currentLayout,
    };

  const handleValuesChange = () => {
    setHasChanges(true);
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      
      // Update terminal config
      updateTerminalConfig({
        fontFamily: values.fontFamily,
        fontSize: values.fontSize,
        lineHeight: values.lineHeight,
        cursorBlink: values.cursorBlink,
        cursorStyle: values.cursorStyle,
        theme: values.theme === 'dark' ? TERMINAL_THEME.DARK : TERMINAL_THEME.LIGHT,
      });
      
      // Update keyboard layout
      setKeyboardLayout(values.keyboardLayout);
      
      setHasChanges(false);
      message.success('Settings saved successfully');
      onClose();
    } catch (error) {
      console.error('Failed to save settings:', error);
      message.error('Failed to save settings');
    }
  };

  const handleReset = () => {
    form.setFieldsValue(initialValues);
    setHasChanges(false);
  };

  const handleCancel = () => {
    if (hasChanges) {
      Modal.confirm({
        title: 'Unsaved Changes',
        content: 'You have unsaved changes. Are you sure you want to close?',
        okText: 'Yes, close',
        cancelText: 'Cancel',
        onOk: () => {
          form.setFieldsValue(initialValues);
          setHasChanges(false);
          onClose();
        },
      });
    } else {
      onClose();
    }
  };

  return (
    <StyledModal
      title={
        <>
          <SettingOutlined />
          Settings
        </>
      }
      open={open}
      onCancel={handleCancel}
      width={600}
      footer={[
        <Button key="reset" onClick={handleReset} disabled={!hasChanges}>
          Reset
        </Button>,
        <Button key="cancel" onClick={handleCancel}>
          Cancel
        </Button>,
        <Button key="save" type="primary" onClick={handleSave} disabled={!hasChanges}>
          Save Changes
        </Button>,
      ]}
    >
      <StyledForm
        form={form}
        layout="vertical"
        initialValues={initialValues}
        onValuesChange={handleValuesChange}
      >
        <SectionTitle level={4}>Terminal Settings</SectionTitle>
        
        <Form.Item
          label="Font Family"
          name="fontFamily"
          rules={[{ required: true, message: 'Please enter font family' }]}
        >
          <Input placeholder="e.g., Menlo, Monaco, Courier New" />
        </Form.Item>

        <Form.Item
          label="Font Size"
          name="fontSize"
          rules={[{ required: true, message: 'Please select font size' }]}
        >
          <Slider
            min={10}
            max={24}
            marks={{
              10: '10px',
              12: '12px',
              14: '14px',
              16: '16px',
              18: '18px',
              20: '20px',
              24: '24px',
            }}
          />
        </Form.Item>

        <Form.Item
          label="Line Height"
          name="lineHeight"
          rules={[{ required: true, message: 'Please select line height' }]}
        >
          <Slider
            min={1.0}
            max={2.0}
            step={0.1}
            marks={{
              1.0: '1.0',
              1.2: '1.2',
              1.4: '1.4',
              1.6: '1.6',
              1.8: '1.8',
              2.0: '2.0',
            }}
          />
        </Form.Item>

        <Form.Item label="Cursor Style" name="cursorStyle">
          <Select>
            <Option value="block">Block</Option>
            <Option value="underline">Underline</Option>
            <Option value="bar">Bar</Option>
          </Select>
        </Form.Item>

        <Form.Item label="Cursor Blink" name="cursorBlink" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item label="Theme" name="theme">
          <Select>
            <Option value="dark">Dark</Option>
            <Option value="light">Light</Option>
          </Select>
        </Form.Item>

        <Divider />

        <SectionTitle level={4}>Keyboard Settings</SectionTitle>

        <Form.Item label="Virtual Keyboard Layout" name="keyboardLayout">
          <Select>
            <Option value={KEYBOARD_LAYOUTS.QWERTY}>QWERTY</Option>
            <Option value="function">Function Keys</Option>
          </Select>
        </Form.Item>

        <Divider />

        <Text type="secondary" style={{ fontSize: '12px' }}>
          Settings are automatically saved to your browser's local storage.
        </Text>
      </StyledForm>
    </StyledModal>
  );
};

export default SettingsModal;
