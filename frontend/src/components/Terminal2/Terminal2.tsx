import type { TerminalProps } from "@/types";
import { useEffect, useRef } from "react";
import { styled } from "styled-components";

const TerminalContainer = styled.div`
  width: 100%;
  height: 100%;
  background: #000;
  border-radius: 4px;
  overflow: hidden;
  box-sizing: border-box;
  /* Ensure it fills the parent */
  min-height: 700px;

  .xterm {
    width: 100%;
    height: 100%;
  }

  .xterm-viewport {
    background: transparent !important;
    overflow: hidden !important; 
  }

  .xterm-screen {
    background: transparent !important;
  }
`;

const Terminal2: React.FC<TerminalProps> = ({ className, onReady }) => {
    const terminalRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        console.log('Terminal2 mounted');
        onReady?.();
    }, [onReady]);

    return (
        <TerminalContainer className={className}>
            <div ref={terminalRef} style={{ backgroundColor: '#F5DEB3', color: '#00BFFF', width: '100%', height: '100%' }}>
            Hello World!!!
            </div>
        </TerminalContainer>
    )
}

export default Terminal2;
