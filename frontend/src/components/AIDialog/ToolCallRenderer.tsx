import React from 'react';
import { Card, Tag, Spin } from 'antd';
import { CheckCircleOutlined, ExclamationCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import type { ToolCall } from '@/types';

const ToolCallCard = styled(Card)`
  margin: 2px 0;
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  background:rgb(210, 208, 208);
  
  .ant-card-body {
    padding: 5px 10px;
  }
`;

const ToolCallHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0px;
  cursor: pointer;
  &:hover {
    background:rgb(182, 182, 182);
    border-radius: 4px;
  }
`;

const ToolCallName = styled.span`
  font-weight: 600;
  color: #1890ff;
  font-size: 14px;
`;

const ToolCallArguments = styled.pre`
  background: #f5f5f5;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 8px;
  margin: 8px 0;
  font-size: 12px;
  font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 200px;
  overflow-y: auto;
  color: #060606;
`;

const ToolCallResult = styled.div`
  background: #f0f9ff;
  border: 1px solid #bae7ff;
  border-radius: 4px;
  padding: 8px;
  margin-top: 8px;
  font-size: 12px;
  max-height: 300px;
  overflow-y: auto;
  color: #060606;
`;

const ToolCallError = styled.div`
  background: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 4px;
  padding: 8px;
  margin-top: 8px;
  font-size: 12px;
  color: #ff4d4f;
`;

interface ToolCallRendererProps {
  toolCall: ToolCall;
}

const ToolCallRenderer: React.FC<ToolCallRendererProps> = ({ toolCall }) => {

  const [toggleShowBody, setToggleShowBody] = React.useState(false);

  const getStatusIcon = () => {
    switch (toolCall.status) {
      case 'pending':
        return <LoadingOutlined style={{ color: '#1890ff' }} />;
      case 'running':
        return <Spin size="small" />;
      case 'complete':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <LoadingOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const getStatusTag = () => {
    const statusColors = {
      pending: 'blue',
      running: 'processing',
      complete: 'success',
      error: 'error'
    };

    const statusTexts = {
      pending: 'Pending',
      running: 'Running',
      complete: 'Complete',
      error: 'Error'
    };

    return (
      <Tag 
        color={statusColors[toolCall.status || 'pending']} 
        icon={getStatusIcon()}
        style={{ fontSize: '11px' }}
      >
        {statusTexts[toolCall.status || 'pending']}
      </Tag>
    );
  };

  const formatArguments = (args: string) => {
    try {
      const parsed = JSON.parse(args);
      return JSON.stringify(parsed, null, 2);
    } catch {
      return args;
    }
  };

  const formatResult = (result: any) => {
    if (typeof result === 'string') {
      try {
        const parsed = JSON.parse(result);
        return JSON.stringify(parsed, null, 2);
      } catch {
        return result;
      }
    }
    return JSON.stringify(result, null, 2);
  };

  return (
    <ToolCallCard size="small">
      <ToolCallHeader onClick={() => setToggleShowBody(!toggleShowBody)}>
        <ToolCallName>{toolCall.function.name}</ToolCallName>
        {getStatusTag()}
      </ToolCallHeader>
      
      {toggleShowBody && toolCall.function.arguments && (
        <div>
          <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>
            Arguments:
          </div>
          <ToolCallArguments>
            {formatArguments(toolCall.function.arguments)}
          </ToolCallArguments>
        </div>
      )}
      
      {toggleShowBody && toolCall.result && (
        <div>
          <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>
            Result:
          </div>
          <ToolCallResult>
            {formatResult(toolCall.result)}
          </ToolCallResult>
        </div>
      )}
      
      {toggleShowBody && toolCall.error && (
        <div>
          <div style={{ fontSize: '12px', color: '#ff4d4f', marginBottom: '4px' }}>
            Error:
          </div>
          <ToolCallError>
            {toolCall.error}
          </ToolCallError>
        </div>
      )}
    </ToolCallCard>
  );
};

export default ToolCallRenderer;
