import { create } from 'zustand';
import type { TerminalState, TerminalConfig } from '@/types';
import { TERMINAL_CONFIG, TERMINAL_THEME, STORAGE_KEYS } from '@/constants';

interface TerminalStore extends TerminalState {
  terminal: any; // xterm.js Terminal instance
  fitAddon: any; // FitAddon instance
  
  // Actions
  setTerminal: (terminal: any) => void;
  setFitAddon: (fitAddon: any) => void;
  setReady: (ready: boolean) => void;
  updateConfig: (config: Partial<TerminalConfig>) => void;
  addToBuffer: (data: string) => void;
  clearBuffer: () => void;
  saveConfig: () => void;
  loadConfig: () => void;
  resize: () => void;
}

// Load saved config from localStorage
const loadSavedConfig = (): TerminalConfig => {
  try {
    const saved = localStorage.getItem(STORAGE_KEYS.TERMINAL_CONFIG);
    if (saved) {
      const parsedConfig = JSON.parse(saved);
      return {
        ...getDefaultConfig(),
        ...parsedConfig,
      };
    }
  } catch (error) {
    console.warn('Failed to load terminal config from localStorage:', error);
  }
  return getDefaultConfig();
};

// Get default terminal configuration
const getDefaultConfig = (): TerminalConfig => ({
  fontFamily: TERMINAL_CONFIG.FONT_FAMILY,
  fontSize: TERMINAL_CONFIG.FONT_SIZE,
  lineHeight: TERMINAL_CONFIG.LINE_HEIGHT,
  cursorBlink: TERMINAL_CONFIG.CURSOR_BLINK,
  cursorStyle: TERMINAL_CONFIG.CURSOR_STYLE,
  theme: TERMINAL_THEME.DARK,
});

export const useTerminalStore = create<TerminalStore>((set, get) => ({
  // Initial state
  terminal: null,
  fitAddon: null,
  isReady: false,
  buffer: [],
  config: loadSavedConfig(),

  // Actions
  setTerminal: (terminal: any) => {
    set({ terminal });
    // Note: Terminal options should be set during construction in the component
    // We don't modify options here to avoid xterm.js readonly option errors
  },

  setFitAddon: (fitAddon: any) => set({ fitAddon }),

  setReady: (ready: boolean) => set({ isReady: ready }),

  updateConfig: (configUpdate: Partial<TerminalConfig>) => {
    const state = get();
    const newConfig = { ...state.config, ...configUpdate };

    set({ config: newConfig });

    // Note: Terminal options cannot be modified after creation
    // The Terminal component should recreate the terminal instance with new config
    // when config changes are detected

    // Save to localStorage
    get().saveConfig();
  },

  addToBuffer: (data: string) => {
    set((state) => ({
      buffer: [...state.buffer, data].slice(-1000), // Keep last 1000 entries
    }));
  },

  clearBuffer: () => set({ buffer: [] }),

  saveConfig: () => {
    try {
      const state = get();
      localStorage.setItem(STORAGE_KEYS.TERMINAL_CONFIG, JSON.stringify(state.config));
    } catch (error) {
      console.warn('Failed to save terminal config to localStorage:', error);
    }
  },

  loadConfig: () => {
    const config = loadSavedConfig();
    set({ config });

    // Note: Terminal options cannot be modified after creation
    // The Terminal component should recreate the terminal instance with new config
    // when config changes are detected
  },

  resize: () => {
    const state = get();
    if (state.fitAddon && state.terminal) {
      try {
        state.fitAddon.fit();
      } catch (error) {
        console.warn('Failed to resize terminal:', error);
      }
    }
  },
}));

// Selector hooks for better performance
export const useTerminalConfig = () => useTerminalStore((state) => state.config);
export const useTerminalReady = () => useTerminalStore((state) => state.isReady);
export const useTerminalActions = () => useTerminalStore((state) => state.updateConfig);
