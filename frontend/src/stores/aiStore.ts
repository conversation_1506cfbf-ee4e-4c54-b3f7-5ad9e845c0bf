import { create } from 'zustand';
import type { AIDialogState, AIMessage } from '@/types';

interface AIStore extends AIDialogState {
  sessionId: string;
  isStreaming: boolean;

  // Actions
  setOpen: (open: boolean) => void;
  toggle: () => void;
  addMessage: (message: Omit<AIMessage, 'id'>) => void;
  updateMessage: (id: string, updates: Partial<AIMessage>) => void;
  removeMessage: (id: string) => void;
  clearMessages: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setStreaming: (streaming: boolean) => void;
  generateSessionId: () => string;
  resetSession: () => void;

  // New methods for tool calling support
  handleStreamingChunk: (chunk: StreamingChunk) => void;
  startStreamingMessage: () => void;
  finishStreamingMessage: () => void;
  handleToolCallEvent: (event: ToolCallEvent) => void;
}

// Streaming chunk interface for OpenAI SDK compatibility
interface StreamingChunk {
  type: 'content' | 'tool_call';
  content?: string;
  fullContent?: string;
  toolCall?: {
    id?: string;
    index?: number;
    type?: string;
    function?: {
      name?: string;
      arguments?: string;
    };
    status?: 'pending' | 'running' | 'complete' | 'error';
  };
  allToolCalls?: any[];
}

// Tool call event interface
export interface ToolCallEvent {
  type: 'start' | 'complete' | 'error';
  toolCall: {
    id: string;
    function: {
      name: string;
      arguments: string;
    };
  };
  result?: any;
  error?: string;
  count: number;
}

// Generate unique session ID
const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

export const useAIStore = create<AIStore>((set, get) => ({
  // Initial state
  isOpen: false,
  messages: [],
  isLoading: false,
  error: null,
  sessionId: generateSessionId(),
  isStreaming: false,

  // Actions
  setOpen: (open: boolean) => set({ isOpen: open }),

  toggle: () => set((state) => ({ isOpen: !state.isOpen })),

  addMessage: (messageData: Omit<AIMessage, 'id'>) => {
    const message: AIMessage = {
      ...messageData,
      id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      timestamp: messageData.timestamp || new Date(),
    };
    
    set((state) => ({
      messages: [...state.messages, message],
    }));
  },

  updateMessage: (id: string, updates: Partial<AIMessage>) => {
    set((state) => ({
      messages: state.messages.map((msg) =>
        msg.id === id ? { ...msg, ...updates } : msg
      ),
    }));
  },

  removeMessage: (id: string) => {
    set((state) => ({
      messages: state.messages.filter((msg) => msg.id !== id),
    }));
  },

  clearMessages: () => set({ messages: [] }),

  setLoading: (loading: boolean) => set({ isLoading: loading }),

  setError: (error: string | null) => set({ error }),

  setStreaming: (streaming: boolean) => set({ isStreaming: streaming }),

  generateSessionId,

  resetSession: () => {
    const newSessionId = generateSessionId();
    set({
      sessionId: newSessionId,
      messages: [],
      isLoading: false,
      error: null,
      isStreaming: false,
    });
  },

  // New methods for tool calling support
  handleStreamingChunk: (chunk: StreamingChunk) => {
    const state = get();
    const lastMessage = state.messages[state.messages.length - 1];
    const lastMixedContent = lastMessage?.mixedContent;
    let lastMixedMessage = lastMixedContent[lastMixedContent.length - 1];

    if (chunk.type === 'content') {
      if (lastMixedMessage?.type !== 'content') {
        lastMixedMessage = {
          type: 'content',
          content: ''
        };
        lastMixedContent.push(lastMixedMessage);
      }

      // Update content
      lastMixedMessage.content += chunk.content || '';
    } else if (chunk.type === 'tool_call') {
      if (lastMixedMessage?.type !== 'tool_call') {
        lastMixedMessage = {
          type: 'tool_call',
          toolCall: {
            id: chunk.toolCall?.id || '',
            function: {
              name: '',
              arguments: ''
            },
            status: 'pending',
            result: '',
            error: ''
          }
        };
        lastMixedContent.push(lastMixedMessage);
      }
      lastMixedMessage.toolCall!.function.arguments += chunk.toolCall?.function?.arguments || '';
      lastMixedMessage.toolCall!.function.name += chunk.toolCall?.function?.name || '';
    }

    state.updateMessage(lastMessage.id, lastMessage);
  },

  startStreamingMessage: () => {
    get().addMessage({
      role: 'assistant',
      mixedContent: [{ type: 'content', content: '' }],
      timestamp: new Date(),
    });
  },

  finishStreamingMessage: () => {
    // const state = get();

    // // Create a new message with mixed content
    // const message: AIMessage = {
    //   id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
    //   role: 'assistant',
    //   // content: state.currentStreamingMessage,
    //   timestamp: new Date(),
    //   isStreaming: false,
    //   // mixedContent: [...state.mixedStreamingMessage]
    // };

    // set((state) => ({
    //   messages: [...state.messages, message],
    //   isStreaming: false,
    //   // currentStreamingMessage: '',
    //   // mixedStreamingMessage: []
    // }));
  },

  handleToolCallEvent: (event: ToolCallEvent) => {
    const state = get();
    const lastMessage = state.messages[state.messages.length - 1];
    if (!lastMessage?.mixedContent) return;

    // Find the tool call that matches the event's tool call ID
    for (let i = 0; i < lastMessage.mixedContent.length; i++) {
      const message = lastMessage.mixedContent[i];
      if (message.type === 'tool_call' && 
          message.toolCall && 
          message.toolCall.id === event.toolCall.id) {
        
        const eventTypeMap: Record<ToolCallEvent['type'], 'running' | 'complete' | 'error'> = {
          start: 'running',
          complete: 'complete',
          error: 'error'
        };

        // Update the matching tool call
        lastMessage.mixedContent[i] = {
          ...message,
          toolCall: {
            ...message.toolCall,
            status: eventTypeMap[event.type],
            result: event.result,
            error: event.error
          }
        };

        state.updateMessage(lastMessage.id, lastMessage);
        break;
      }
    }
  },
}));

// Selector hooks for better performance
export const useAIDialogVisibility = () => useAIStore((state) => state.isOpen);
export const useAIMessages = () => useAIStore((state) => state.messages);
export const useAILoading = () => useAIStore((state) => state.isLoading);
export const useAIError = () => useAIStore((state) => state.error);
export const useIsStreaming = () => useAIStore((state) => state.isStreaming);
export const useAISession = () => useAIStore((state) => state.sessionId);
export const useSetOpen = () => useAIStore((state) => state.setOpen);
export const useToggle = () => useAIStore((state) => state.toggle);
export const useAddMessage = () => useAIStore((state) => state.addMessage);
export const useUpdateMessage = () => useAIStore((state) => state.updateMessage);
export const useRemoveMessage = () => useAIStore((state) => state.removeMessage);
export const useClearMessages = () => useAIStore((state) => state.clearMessages);
export const useSetLoading = () => useAIStore((state) => state.setLoading);
export const useSetError = () => useAIStore((state) => state.setError);
export const useSetStreaming = () => useAIStore((state) => state.setStreaming);
export const useResetSession = () => useAIStore((state) => state.resetSession);

// New selector hooks for tool calling support
export const useHandleStreamingChunk = () => useAIStore((state) => state.handleStreamingChunk);
export const useStartStreamingMessage = () => useAIStore((state) => state.startStreamingMessage);
export const useFinishStreamingMessage = () => useAIStore((state) => state.finishStreamingMessage);
export const useHandleToolCallEvent = () => useAIStore((state) => state.handleToolCallEvent);
