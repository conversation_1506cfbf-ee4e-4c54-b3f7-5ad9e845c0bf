import type { WebSocketManager as IWebSocketManager } from '@/types';
import { WEBSOCKET_CONFIG, API_ENDPOINTS } from '@/constants';

export class WebSocketManager implements IWebSocketManager {
  private url: string;
  private socket: WebSocket | null = null;
  private reconnectAttempts = 0;
  private reconnectTimer: number | null = null;
  private shouldReconnect = true;
  private connectionState: 'disconnected' | 'connecting' | 'connected' | 'reconnecting' = 'disconnected';

  // Event handlers
  public onOpen?: () => void;
  public onMessage?: (event: MessageEvent) => void;
  public onClose?: (event: CloseEvent) => void;
  public onError?: (event: Event) => void;

  constructor(url?: string) {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    this.url = url || `${protocol}//${window.location.host}${API_ENDPOINTS.WEBSOCKET}`;
  }

  connect(): void {
    if (this.socket && (this.socket.readyState === WebSocket.CONNECTING || this.socket.readyState === WebSocket.OPEN)) {
      console.log('Already connected or connecting');
      return;
    }

    this.connectionState = 'connecting';
    console.log(`Connecting to ${this.url}`);

    try {
      this.socket = new WebSocket(this.url);
      this.setupEventListeners();
    } catch (error) {
      console.error(`Failed to create WebSocket: ${error}`);
      this.handleConnectionError();
    }
  }

  disconnect(): void {
    this.shouldReconnect = false;
    
    if (this.reconnectTimer) {
      window.clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.socket) {
      this.socket.close(1000, 'Manual disconnect');
    }

    this.connectionState = 'disconnected';
    console.log('WebSocket connection closed manually');
  }

  send(data: string): boolean {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      try {
        this.socket.send(data);
        return true;
      } catch (error) {
        console.error('Failed to send data:', error);
        return false;
      }
    } else {
      console.warn('Cannot send data: WebSocket is not connected');
      return false;
    }
  }

  getConnectionState(): string {
    return this.connectionState;
  }

  private setupEventListeners(): void {
    if (!this.socket) return;

    this.socket.onopen = () => {
      console.log('WebSocket connection established');
      this.connectionState = 'connected';
      this.reconnectAttempts = 0;
      
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }

      this.onOpen?.();
    };

    this.socket.onmessage = (event) => {
      this.onMessage?.(event);
    };

    this.socket.onclose = (event) => {
      console.log(`WebSocket connection closed: ${event.code} - ${event.reason}`);
      this.connectionState = 'disconnected';
      
      this.onClose?.(event);

      // Attempt to reconnect if not manually closed
      if (this.shouldReconnect && event.code !== 1000) {
        this.handleConnectionError();
      }
    };

    this.socket.onerror = (event) => {
      console.error('WebSocket error occurred');
      this.onError?.(event);
      this.handleConnectionError();
    };
  }

  private handleConnectionError(): void {
    if (!this.shouldReconnect) return;

    if (this.reconnectAttempts >= WEBSOCKET_CONFIG.MAX_RECONNECT_ATTEMPTS) {
      console.error('Max reconnection attempts reached');
      this.connectionState = 'disconnected';
      return;
    }

    this.connectionState = 'reconnecting';
    this.reconnectAttempts++;

    const delay = Math.min(
      WEBSOCKET_CONFIG.RECONNECT_INTERVAL * Math.pow(WEBSOCKET_CONFIG.RECONNECT_DECAY, this.reconnectAttempts - 1),
      WEBSOCKET_CONFIG.MAX_RECONNECT_INTERVAL
    );

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${WEBSOCKET_CONFIG.MAX_RECONNECT_ATTEMPTS})`);

    this.reconnectTimer = window.setTimeout(() => {
      this.connect();
    }, delay);
  }

  // Getter for socket instance (for compatibility with old code)
  get socketInstance(): WebSocket | null {
    return this.socket;
  }

  // Check if connected
  get isConnected(): boolean {
    return this.socket?.readyState === WebSocket.OPEN;
  }

  // Check if connecting
  get isConnecting(): boolean {
    return this.connectionState === 'connecting';
  }

  // Get reconnect attempts
  get reconnectAttemptsCount(): number {
    return this.reconnectAttempts;
  }
}

// Export a default instance for global use
export const defaultWebSocketManager = new WebSocketManager();

export default WebSocketManager;
