/**
 * AI Agent Service
 * 
 * This service implements a sophisticated AI agent with:
 * - Streaming support
 * - Multi-turn tool calling
 * - Configurable tools and system prompts
 * - Session management
 */

import xtermService from "./xtermService";

// Types
interface AIAgentConfig {
  apiKey: string;
  endpoint?: string;
  maxTokens?: number;
  temperature?: number;
  stream?: boolean;
}

interface AIAgentOptions extends Partial<AIAgentConfig> {
  systemPrompt?: string;
  sessionId?: string;
  model?: string;
  tools?: Tool[];
  maxToolCallIterations?: number;
  onChunk?: (chunk: StreamingChunk) => void;
  onToolCall?: (event: ToolCallEvent) => void;
  onComplete?: (response: AIResponse) => void;
  onError?: (error: Error) => void;
}

interface Tool {
  function: {
    name: string;
    description: string;
    parameters: any;
  };
  execute: (args: any) => Promise<any>;
}

interface StreamingChunk {
  type: 'content' | 'tool_call';
  content?: string;
  fullContent?: string;
  toolCall?: any;
  allToolCalls?: any[];
}

interface ToolCallEvent {
  type: 'start' | 'complete' | 'error';
  toolCall: any;
  result?: any;
  error?: string;
  count: number;
}

interface AIResponse {
  content: string;
  tool_calls?: any[];
  finish_reason: string;
}

interface AIMessage {
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  tool_calls?: any[];
  tool_call_id?: string;
  timestamp: string;
}

/**
 * AI Agent Class
 * Encapsulates an AI agent with system prompt, session, model, tools, and iteration limits
 */
export class AIAgent {
  private systemPrompt: string;
  private sessionId: string;
  private model: string;
  private tools: Tool[];
  private maxToolCallIterations: number;
  private config: AIAgentConfig;
  private messages: AIMessage[];
  private toolCallCount: number;
  private isReady: boolean;
  private isPaused: boolean;
  private isTerminated: boolean;
  private pausePromise: Promise<void> | null;
  private pauseResolve: (() => void) | null;
  private isProcessing: boolean;
  private onChunk: ((chunk: StreamingChunk) => void) | null;
  private onToolCall: ((event: ToolCallEvent) => void) | null;
  private onComplete: ((response: AIResponse) => void) | null;
  private onError: ((error: Error) => void) | null;

  constructor(options: AIAgentOptions = {}) {
    this.systemPrompt = options.systemPrompt || this.getDefaultSystemPrompt();
    this.sessionId = options.sessionId || this.generateSessionId();
    this.model = options.model || 'gpt-3.5-turbo';
    this.tools = options.tools || [];
    this.maxToolCallIterations = options.maxToolCallIterations || 30;
    
    // Configuration
    this.config = {
      apiKey: options.apiKey || '',
      endpoint: options.endpoint || 'https://api.openai.com/v1/chat/completions',
      maxTokens: options.maxTokens || 1000,
      temperature: options.temperature || 0.7,
      stream: options.stream !== false // Default to streaming
    };
    
    // Session state
    this.messages = [];
    this.toolCallCount = 0;
    this.isReady = false;

    // Control state for pause, resume, and terminate
    this.isPaused = false;
    this.isTerminated = false;
    this.pausePromise = null;
    this.pauseResolve = null;
    this.isProcessing = false;

    // Event handlers for streaming
    this.onChunk = options.onChunk || null;
    this.onToolCall = options.onToolCall || null;
    this.onComplete = options.onComplete || null;
    this.onError = options.onError || null;
  }

  /**
   * Get default system prompt
   */
  private getDefaultSystemPrompt(): string {
    return `你是一个交互式终端（macOS系统/bin/bash）自动化专家，正在与一个交互式终端协同工作，努力完成用户提交的任务。你有一个向终端输入的工具箱，要灵活运用该工具，优先将命令组合起来批量执行(composites 模式)，尽最大努力完成用户提交的任务，自主决策，不要过多向用户询问。

你的工具箱包含以下能力：
1. **terminal_input** - 向终端发送输入(优先使用：composites 模式)
   - single_key: 发送单个按键（如 Enter, Tab, Escape, ArrowUp 等）
   - key_combo: 发送组合键（如 Ctrl+C, Ctrl+Z, Alt+F4 等）
   - command_line: 发送单个命令行（如 ls -la, pwd, cat file.txt）
   - composites: 组合以上3种模式的序列（优先使用本模式）

2. **wait** - 等待指定时间
   - 在需要等待命令执行、界面加载等场景时使用

例如你可以精准使用工具完成以下场景：

**场景1：中断正在运行的程序**
- 使用 terminal_input 工具，key_combo 模式发送 Ctrl+C

**场景2：查找并编辑文件内容**
- 使用 command_line 模式输入：vi /tmp/nginx.conf
- 如果未找到，使用 single_key 模式导航到下一页
- 找到后，使用 composites 模式移动光标到目标位置

**场景3：SSH连接远程服务器**
- 输入SSH连接命令
- 检查输出是否有信任主机提示
- 如有提示则输入"yes"，否则输入密码
- 执行远程命令

**场景4：文件操作和管理**
- 使用 composites 模式组合 ls, cd, cp, mv, rm 等命令进行文件操作

**场景5：系统监控和诊断**
- 使用 composites 模式组合 ps, top, df, du, netstat, lsof 等命令查看系统状态

要点：
- 积极主动，自主决策，高效使用工具完成任务
- 根据终端输出动态调整策略，确保对当前状态有准确理解
- 合理使用等待工具，确保命令执行完成后再获取输出
`;
  }

  /**
   * Generate a unique session ID
   */
  private generateSessionId(): string {
    return 'agent_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
  }

  /**
   * Initialize the agent
   */
  async initialize(): Promise<boolean> {
    if (!this.config.apiKey) {
      throw new Error('API key is required');
    }
    
    try {
      // Test the API connection
      await this.testConnection();
      this.isReady = true;
      console.log('AI Agent initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize AI Agent:', error);
      throw error;
    }
  }

  /**
   * Test API connection
   */
  private async testConnection(): Promise<boolean> {
    const response = await fetch(this.config.endpoint!, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        model: this.model,
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 10,
        stream: false
      })
    });

    if (!response.ok) {
      throw new Error(`API test failed: ${response.status} ${response.statusText}`);
    }

    return true;
  }

  /**
   * Add a tool to the agent
   */
  addTool(tool: Tool): void {
    this.tools.push(tool);
  }

  /**
   * Remove a tool from the agent
   */
  removeTool(toolName: string): void {
    this.tools = this.tools.filter(tool => tool.function.name !== toolName);
  }

  /**
   * Get tool definitions for OpenAI API
   */
  private getToolDefinitions(): any[] {
    return this.tools.map(tool => ({
      type: 'function',
      function: tool.function
    }));
  }

  /**
   * Process a user message with streaming support and multi-turn tool calling
   */
  async processMessage(userMessage: string, context: any = {}): Promise<AIResponse> {
    if (!this.isReady) {
      throw new Error('Agent not initialized');
    }

    // Check if agent is terminated
    if (this.isTerminated) {
      throw new Error('Agent has been terminated');
    }

    // Set processing state
    this.isProcessing = true;

    try {
      // Add user message to session
      this.messages.push({
        role: 'user',
        content: userMessage,
        timestamp: new Date().toISOString()
      });

      // Reset tool call count for this interaction
      this.toolCallCount = 0;

      const result = await this.runConversationLoop(context);
      return result;
    } catch (error) {
      if (this.onError) {
        this.onError(error as Error);
      }
      throw error;
    } finally {
      // Clear processing state
      this.isProcessing = false;
    }
  }

  /**
   * Run the conversation loop with tool calling
   */
  private async runConversationLoop(context: any = {}): Promise<AIResponse> {
    let iteration = 0;

    while (iteration < this.maxToolCallIterations) {
      // Check for termination at the start of each iteration
      if (this.isTerminated) {
        console.log('Conversation loop terminated by user');
        throw new Error('Agent terminated by user');
      }

      await this.waitForResumeIfPaused(); // if paused, wait for resume or timeout

      // Check for termination again after potential pause
      if (this.isTerminated) {
        console.log('Conversation loop terminated during pause');
        throw new Error('Agent terminated by user');
      }

      iteration++;

      // Prepare messages for API call
      // 动态获取终端屏幕内容和光标位置，插入systemPrompt前
      let terminalScreenXml = '';
      
      const screenText = xtermService.getAllContent();
      const cursorResult = xtermService.getCursor();
      const cursorX = cursorResult.cursorX;
      const cursorY = cursorResult.cursorY;

      terminalScreenXml = `\n[附!!!]:<terminalScreen><![CDATA[${screenText}]]></terminalScreen>\n<cursor x="${cursorX}" y="${cursorY}"/>\n<!-- <terminalScreen>标签内容为当前终端屏幕所有文本，<cursor>标签内容为光标位置，要始终以该重要信息为准决策下一步操作，这一点很重要!!!不要向用户解释屏幕内容及光标位置 -->\n`;
      
      const messages = [
        { role: 'system' as const, content: this.systemPrompt },
        ...this.messages.map(msg => ({
          role: msg.role,
          content: msg.content,
          tool_calls: msg.tool_calls,
          tool_call_id: msg.tool_call_id
        }))
      ];

      // Add terminal screen content to the last message
      if (messages.length > 0) {
        const lastMessage = messages[messages.length - 1];
        lastMessage.content += terminalScreenXml;
      }

      // Make API call
      const response = await this.callOpenAI(messages);

      // Check for termination after API call
      if (this.isTerminated) {
        console.log('Conversation loop terminated after API call');
        throw new Error('Agent terminated by user');
      }

      if (response.finish_reason === 'stop') {
        // Conversation complete
        if (this.onComplete) {
          this.onComplete(response);
        }
        return response;
      } else if (response.finish_reason === 'tool_calls') {
        // Execute tool calls and continue
        await this.executeToolCalls(response.tool_calls);
        continue;
      } else if (response.finish_reason === 'length') {
        // Token limit reached
        console.warn('Token limit reached');
        if (this.onComplete) {
          this.onComplete(response);
        }
        return response;
      }
    }

    throw new Error(`Maximum tool call iterations (${this.maxToolCallIterations}) exceeded`);
  }

  /**
   * Call OpenAI API with streaming support
   */
  private async callOpenAI(messages: any[]): Promise<AIResponse> {
    const requestBody: any = {
      model: this.model,
      messages: messages,
      max_tokens: this.config.maxTokens,
      temperature: this.config.temperature,
      stream: this.config.stream,
      parallel_tool_calls: false
    };

    // Add tools if available
    if (this.tools.length > 0) {
      requestBody.tools = this.getToolDefinitions();
      // requestBody.tool_choice = 'auto';
    }

    const response = await fetch(this.config.endpoint!, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    if (this.config.stream) {
      return await this.handleStreamingResponse(response);
    } else {
      const data = await response.json();
      const message = data.choices[0].message;

      // Add assistant message to session
      this.messages.push({
        role: 'assistant',
        content: message.content,
        tool_calls: message.tool_calls,
        timestamp: new Date().toISOString()
      });

      return {
        content: message.content,
        tool_calls: message.tool_calls,
        finish_reason: data.choices[0].finish_reason
      };
    }
  }

  /**
   * Handle streaming response from OpenAI
   */
  private async handleStreamingResponse(response: Response): Promise<AIResponse> {
    const reader = response.body!.getReader();
    const decoder = new TextDecoder();

    let content = '';
    let tool_calls: any[] = [];
    let finish_reason: string | null = null;

    try {
      while (true) {
        await this.waitForResumeIfPaused(); // if paused, wait for resume or timeout
        if(this.getIsTerminated()) {
          this.resetTermination();
          break;
        }
        const { done, value } = await reader.read();

        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);

            if (data === '[DONE]') {
              break;
            }

            try {
              const parsed = JSON.parse(data);
              const delta = parsed.choices[0]?.delta;

              if (delta) {
                // Handle content chunks
                if (delta.content) {
                  content += delta.content;
                  if (this.onChunk) {
                    this.onChunk({
                      type: 'content',
                      content: delta.content,
                      fullContent: content
                    });
                  }
                }

                // Handle tool call chunks
                if (delta.tool_calls) {
                  for (const toolCall of delta.tool_calls) {
                    if (!tool_calls[toolCall.index]) {
                      tool_calls[toolCall.index] = {
                        id: toolCall.id,
                        type: toolCall.type,
                        function: { name: '', arguments: '' }
                      };
                    }

                    if (toolCall.function?.name) {
                      tool_calls[toolCall.index].function.name += toolCall.function.name;
                    }

                    if (toolCall.function?.arguments) {
                      tool_calls[toolCall.index].function.arguments += toolCall.function.arguments;
                    }

                    if (this.onChunk) {
                      this.onChunk({
                        type: 'tool_call',
                        toolCall: toolCall,
                        allToolCalls: tool_calls
                      });
                    }
                  }
                }
              }

              // Check for finish reason
              if (parsed.choices[0]?.finish_reason) {
                finish_reason = parsed.choices[0].finish_reason;
              }
            } catch (e) {
              // Skip invalid JSON lines
              continue;
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    // Add assistant message to session
    this.messages.push({
      role: 'assistant',
      content: content,
      tool_calls: tool_calls.length > 0 ? tool_calls : undefined,
      timestamp: new Date().toISOString()
    });

    return {
      content: content,
      tool_calls: tool_calls.length > 0 ? tool_calls : undefined,
      finish_reason: finish_reason || 'stop'
    };
  }

  /**
   * Execute tool calls
   */
  private async executeToolCalls(toolCalls?: any[]): Promise<void> {
    if (!toolCalls || toolCalls.length === 0) {
      return;
    }

    for (const toolCall of toolCalls) {
      await this.waitForResumeIfPaused(); // if paused, wait for resume or timeout
      if(this.getIsTerminated()) {
        this.resetTermination();
        if (this.onToolCall) {
          this.onToolCall({
            type: 'error',
            toolCall: toolCall,
            error: '用户终止',
            count: this.toolCallCount
          });
        }
        break;
      }
      // Check for termination before each tool call
      try {
        this.toolCallCount++;

        if (this.onToolCall) {
          this.onToolCall({
            type: 'start',
            toolCall: toolCall,
            count: this.toolCallCount
          });
        }

        // Find the tool function
        const tool = this.tools.find(t => t.function.name === toolCall.function.name);

        if (!tool) {
          throw new Error(`Tool not found: ${toolCall.function.name}`);
        }

        // Parse arguments
        let args = {};
        try {
          args = JSON.parse(toolCall.function.arguments);
        } catch (e) {
          throw new Error(`Invalid tool arguments: ${toolCall.function.arguments}`);
        }

        // Execute the tool
        const result = await tool.execute(args);

        // Check for termination after tool execution
        if (this.isTerminated) {
          console.log('Tool execution terminated after tool completion');
          throw new Error('Agent terminated by user');
        }

        // Add tool result to messages
        this.messages.push({
          role: 'tool',
          content: JSON.stringify(result),
          tool_call_id: toolCall.id,
          timestamp: new Date().toISOString()
        });

        if (this.onToolCall) {
          this.onToolCall({
            type: 'complete',
            toolCall: toolCall,
            result: result,
            count: this.toolCallCount
          });
        }

      } catch (error: any) {
        console.error(`Tool execution error for ${toolCall.function.name}:`, error);

        // Add error result to messages
        this.messages.push({
          role: 'tool',
          content: JSON.stringify({ error: error.message }),
          tool_call_id: toolCall.id,
          timestamp: new Date().toISOString()
        });

        if (this.onToolCall) {
          this.onToolCall({
            type: 'error',
            toolCall: toolCall,
            error: error.message,
            count: this.toolCallCount
          });
        }

        // Re-throw termination errors
        if (error.message === 'Agent terminated by user') {
          throw error;
        }
      }
    }
  }

  /**
   * Clear session messages
   */
  clearSession(): void {
    this.messages = [];
    this.toolCallCount = 0;
  }

  /**
   * Get session messages
   */
  getMessages(): AIMessage[] {
    return [...this.messages];
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<AIAgentConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get agent statistics
   */
  getStats() {
    return {
      sessionId: this.sessionId,
      model: this.model,
      toolCount: this.tools.length,
      messageCount: this.messages.length,
      toolCallCount: this.toolCallCount,
      maxIterations: this.maxToolCallIterations,
      isReady: this.isReady,
      isPaused: this.isPaused,
      isTerminated: this.isTerminated,
      isProcessing: this.isProcessing
    };
  }

  /**
   * Pause the agent (wait)
   * Creates a promise that will be resolved when resume is called
   */
  pause(): void {
    if (this.isPaused) {
      console.log('Agent is already paused');
      return;
    }

    this.isPaused = true;
    this.pausePromise = new Promise<void>((resolve) => {
      this.pauseResolve = resolve;
    });

    console.log(`Agent ${this.sessionId} paused`);
  }

  /**
   * Resume the agent (clear wait)
   * Resolves the pause promise and allows the agent to continue
   */
  resume(): void {
    if (!this.isPaused) {
      console.log('Agent is not paused');
      return;
    }

    this.isPaused = false;
    if (this.pauseResolve) {
      this.pauseResolve();
      this.pauseResolve = null;
    }
    this.pausePromise = null;

    console.log(`Agent ${this.sessionId} resumed`);
  }

  /**
   * Terminate the agent
   * Stops all processing and clears any pending operations
   */
  terminate(): void {
    this.isTerminated = true;
    this.isProcessing = false;

    // If paused, resume to allow termination to proceed
    if (this.isPaused) {
      this.resume();
    }

    console.log(`Agent ${this.sessionId} terminated`);
  }

  /**
   * Reset termination flag
   * Allows the agent to be used again after termination
   */
  resetTermination(): void {
    this.isTerminated = false;
    console.log(`Agent ${this.sessionId} termination reset`);
  }

  /**
   * Check if agent is paused
   */
  getIsPaused(): boolean {
    return this.isPaused;
  }

  /**
   * Check if agent is terminated
   */
  getIsTerminated(): boolean {
    return this.isTerminated;
  }

  /**
   * Check if agent is processing
   */
  getIsProcessing(): boolean {
    return this.isProcessing;
  }

  /**
   * Wait for pause to be cleared (with timeout)
   */
  private async waitForResumeIfPaused(timeoutMs: number = 600000): Promise<void> { // 10 minutes default
    if (!this.isPaused) {
      return;
    }

    const timeoutPromise = new Promise<void>((_, reject) => {
      setTimeout(() => reject(new Error('Pause timeout exceeded')), timeoutMs);
    });

    try {
      await Promise.race([this.pausePromise!, timeoutPromise]);
    } catch (error: any) {
      console.warn(`Agent ${this.sessionId} pause timeout:`, error.message);
      // Auto-resume on timeout
      this.resume();
    }
  }
}

/**
 * AI Agent Manager
 * Manages multiple AI agents and provides a unified interface
 */
export class AIAgentManager {
  private agents: Map<string, AIAgent>;
  private defaultConfig: AIAgentConfig;

  constructor() {
    this.agents = new Map();
    this.defaultConfig = {
      apiKey: '',
      endpoint: 'https://api.openai.com/v1/chat/completions',
      maxTokens: 1000,
      temperature: 0.7,
      stream: true
    };
  }

  /**
   * Create a new agent
   */
  createAgent(sessionId: string, options: AIAgentOptions = {}): AIAgent {
    const config = { ...this.defaultConfig, ...options };
    const agent = new AIAgent({
      sessionId: sessionId,
      ...config
    });

    this.agents.set(sessionId, agent);
    return agent;
  }

  /**
   * Get an existing agent
   */
  getAgent(sessionId: string): AIAgent | undefined {
    return this.agents.get(sessionId);
  }

  /**
   * Remove an agent
   */
  removeAgent(sessionId: string): boolean {
    return this.agents.delete(sessionId);
  }

  /**
   * Get all agent sessions
   */
  getAllSessions(): string[] {
    return Array.from(this.agents.keys());
  }

  /**
   * Update default configuration
   */
  updateDefaultConfig(config: Partial<AIAgentConfig>): void {
    this.defaultConfig = { ...this.defaultConfig, ...config };
  }

  /**
   * Get manager statistics
   */
  getStats() {
    return {
      agentCount: this.agents.size,
      sessions: Array.from(this.agents.keys()),
      defaultConfig: { ...this.defaultConfig, apiKey: this.defaultConfig.apiKey ? '***' : '' }
    };
  }
}

// Export singleton instances
export const aiAgentManager = new AIAgentManager();
