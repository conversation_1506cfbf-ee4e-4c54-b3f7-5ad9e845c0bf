import type { XTermService as IXTermService } from '@/types';
import { useTerminalStore } from '@/stores/terminalStore';
import { useWebSocketStore } from '@/stores/websocketStore';

class XTermService implements IXTermService {
  private terminal: any = null;
  private socket: WebSocket | null = null;
  private buffer: string[] = [];

  /**
   * Initialize the service with terminal and socket instances
   */
  initialize(): void {
    const previousTerminal = this.terminal;
    const previousSocket = this.socket;

    this.terminal = useTerminalStore.getState().terminal;
    this.socket = useWebSocketStore.getState().socket;

    if (previousTerminal !== this.terminal) {
      // Listen to terminal output to maintain buffer
      this.terminal?.onData((data: string) => {
        this.buffer.push(data);
      });
      console.log('XTermService terminal changed');
    }
    
    if (previousSocket !== this.socket) {
      console.log('XTermService socket changed');
    }

  }

  /**
   * Check if the service is ready for operations
   */
  isServiceReady(): boolean {
    return this.terminal && 
           this.socket && 
           this.socket.readyState === WebSocket.OPEN;
  }

  /**
   * Send raw data to the terminal via WebSocket
   */
  async sendRawData(data: string): Promise<boolean> {
    if (!this.isServiceReady()) {
      console.error('XTermService not ready');
      return false;
    }

    try {
      this.socket!.send(JSON.stringify({
        type: 'input',
        data: data
      }));
      return true;
    } catch (error) {
      console.error('Failed to send data:', error);
      return false;
    }
  }

  /**
   * Send a single key to the terminal
   */
  async sendKey(key: string): Promise<boolean> {
    const keyMappings: Record<string, string> = {
      'Enter': '\r',
      'Tab': '\t',
      'Backspace': '\x7f',
      'Delete': '\x1b[3~',
      'ArrowUp': '\x1b[A',
      'ArrowDown': '\x1b[B',
      'ArrowRight': '\x1b[C',
      'ArrowLeft': '\x1b[D',
      'Home': '\x1b[H',
      'End': '\x1b[F',
      'PageUp': '\x1b[5~',
      'PageDown': '\x1b[6~',
      'Escape': '\x1b',
      'F1': '\x1bOP',
      'F2': '\x1bOQ',
      'F3': '\x1bOR',
      'F4': '\x1bOS',
      'F5': '\x1b[15~',
      'F6': '\x1b[17~',
      'F7': '\x1b[18~',
      'F8': '\x1b[19~',
      'F9': '\x1b[20~',
      'F10': '\x1b[21~',
      'F11': '\x1b[23~',
      'F12': '\x1b[24~',
    };

    const data = keyMappings[key] || key;
    return await this.sendRawData(data);
  }

  /**
   * Send a key combination (e.g., "Ctrl+C", "Alt+F4")
   */
  async sendKeyCombination(combination: string): Promise<boolean> {
    const parts = combination.split('+').map(part => part.trim());
    
    if (parts.length === 1) {
      return await this.sendKey(parts[0]);
    }

    const modifiers = parts.slice(0, -1).map(mod => mod.toLowerCase());
    const key = parts[parts.length - 1];

    let data = '';

    // Handle common key combinations
    if (modifiers.includes('ctrl')) {
      const ctrlMappings: Record<string, string> = {
        'a': '\x01', 'b': '\x02', 'c': '\x03', 'd': '\x04', 'e': '\x05',
        'f': '\x06', 'g': '\x07', 'h': '\x08', 'i': '\x09', 'j': '\x0a',
        'k': '\x0b', 'l': '\x0c', 'm': '\x0d', 'n': '\x0e', 'o': '\x0f',
        'p': '\x10', 'q': '\x11', 'r': '\x12', 's': '\x13', 't': '\x14',
        'u': '\x15', 'v': '\x16', 'w': '\x17', 'x': '\x18', 'y': '\x19',
        'z': '\x1a',
      };
      
      const lowerKey = key.toLowerCase();
      if (ctrlMappings[lowerKey]) {
        data = ctrlMappings[lowerKey];
      }
    }

    if (data) {
      return await this.sendRawData(data);
    } else {
      console.error('Unsupported key combination:', combination);
      return false;
    }
  }

  /**
   * Send a command line to the terminal
   */
  async sendCommand(command: string, executeImmediately = true): Promise<boolean> {
    let success = await this.sendRawData(command);
    
    if (success && executeImmediately) {
      success = await this.sendKey('Enter');
    }
    
    return success;
  }

  getCursor(): { cursorX: number; cursorY: number } {
    if (!this.terminal) {
      return { cursorX: 0, cursorY: 0 };
    }

    try {
      const buffer = this.terminal.buffer.active;
      return { cursorX: buffer.cursorX, cursorY: buffer.cursorY };
    } catch (error) {
      console.error('Failed to get cursor position:', error);
      return { cursorX: 0, cursorY: 0 };
    }
  }

  /**
   * Get all content from the terminal buffer
   */
  getAllContent(): string {
    if (!this.terminal) {
      return '';
    }

    try {
      const buffer = this.terminal.buffer.active;
      let content = '';
      
      for (let i = 0; i < buffer.length; i++) {
        const line = buffer.getLine(i);
        if (line) {
          content += line.translateToString(true) + '\n';
        }
      }
      
      return content;
    } catch (error) {
      console.error('Failed to get terminal content:', error);
      return '';
    }
  }

  /**
   * Clear the terminal screen
   */
  async clearScreen(): Promise<boolean> {
    return await this.sendKeyCombination('Ctrl+L');
  }

  /**
   * Send interrupt signal (Ctrl+C)
   */
  async sendInterrupt(): Promise<boolean> {
    return await this.sendKeyCombination('Ctrl+C');
  }

  /**
   * Send EOF signal (Ctrl+D)
   */
  async sendEOF(): Promise<boolean> {
    return await this.sendKeyCombination('Ctrl+D');
  }

  /**
   * Get the current buffer content
   */
  getBuffer(): string[] {
    return [...this.buffer];
  }

  /**
   * Clear the internal buffer
   */
  clearBuffer(): void {
    this.buffer = [];
  }

  /**
   * Reset the service
   */
  reset(): void {
    this.terminal = null;
    this.socket = null;
    this.buffer = [];
  }

  /**
   * Enable or disable debug logging
   */
  setDebugLogging(enabled: boolean): void {
    // Debug logging functionality can be implemented here if needed
    console.log('Debug logging', enabled ? 'enabled' : 'disabled');
  }


}

// Export a singleton instance
export const xtermService = new XTermService();
export default xtermService;
