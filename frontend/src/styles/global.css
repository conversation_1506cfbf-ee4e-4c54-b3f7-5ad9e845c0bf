/* Global styles for the Web Terminal application */

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #1e1e1e;
  color: #f0f0f0;
  overflow: hidden;
}

#root {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Ant Design overrides for dark theme */
.ant-layout {
  background: #1e1e1e !important;
}

.ant-layout-header {
  background: #1e1e1e !important;
  border-bottom: 1px solid #444 !important;
}

.ant-layout-footer {
  background: #1e1e1e !important;
  border-top: 1px solid #444 !important;
}

.ant-layout-content {
  background: #1e1e1e !important;
}

/* Button overrides */
.ant-btn {
  border-radius: 4px;
}

.ant-btn-primary {
  background: #52c41a;
  border-color: #52c41a;
}

.ant-btn-primary:hover {
  background: #73d13d;
  border-color: #73d13d;
}

/* Typography overrides */
.ant-typography {
  color: #f0f0f0;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #2a2a2a;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #777;
}

/* Selection styling */
::selection {
  background: rgba(82, 196, 26, 0.3);
  color: #fff;
}

::-moz-selection {
  background: rgba(82, 196, 26, 0.3);
  color: #fff;
}

/* Focus outline */
*:focus {
  outline: 2px solid #52c41a;
  outline-offset: 2px;
}

/* Utility classes */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 {
  gap: 4px;
}

.gap-2 {
  gap: 8px;
}

.gap-3 {
  gap: 12px;
}

.gap-4 {
  gap: 16px;
}

.p-1 {
  padding: 4px;
}

.p-2 {
  padding: 8px;
}

.p-3 {
  padding: 12px;
}

.p-4 {
  padding: 16px;
}

.m-1 {
  margin: 4px;
}

.m-2 {
  margin: 8px;
}

.m-3 {
  margin: 12px;
}

.m-4 {
  margin: 16px;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .hide-mobile {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .hide-desktop {
    display: none !important;
  }
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.fade-out {
  animation: fadeOut 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.slide-down {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}
