// WebSocket configuration
export const WEBSOCKET_CONFIG = {
  MAX_RECONNECT_ATTEMPTS: 10,
  RECONNECT_INTERVAL: 1000,
  MAX_RECONNECT_INTERVAL: 30000,
  RECONNECT_DECAY: 1.5,
  TIMEOUT_INTERVAL: 2000,
} as const;

// Terminal configuration
export const TERMINAL_CONFIG = {
  FONT_FAMILY: 'Menlo, Monaco, "Courier New", monospace',
  FONT_SIZE: 14,
  LINE_HEIGHT: 1.2,
  CURSOR_BLINK: true,
  CURSOR_STYLE: 'block' as const,
} as const;

// Terminal theme
export const TERMINAL_THEME = {
  DARK: {
    background: '#000000',
    foreground: '#f0f0f0',
    cursor: '#f0f0f0',
    selection: 'rgba(255, 255, 255, 0.3)',
  },
  LIGHT: {
    background: '#ffffff',
    foreground: '#000000',
    cursor: '#000000',
    selection: 'rgba(0, 0, 0, 0.3)',
  },
} as const;

// Virtual keyboard layouts
export const KEYBOARD_LAYOUTS = {
  QWERTY: 'qwerty',
  DVORAK: 'dvorak',
  COLEMAK: 'colemak',
} as const;

// API endpoints
export const API_ENDPOINTS = {
  WEBSOCKET: '/ws',
  SESSION_EXTEND: '/api/extend',
  SESSION_STATUS: '/api/status',
} as const;

// UI constants
export const UI_CONSTANTS = {
  HEADER_HEIGHT: 64,
  FOOTER_HEIGHT: 40,
  SIDEBAR_WIDTH: 240,
  SIDEBAR_COLLAPSED_WIDTH: 64,
} as const;

// Animation durations (in milliseconds)
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
} as const;

// Z-index values
export const Z_INDEX = {
  DROPDOWN: 1000,
  MODAL: 1050,
  POPOVER: 1060,
  TOOLTIP: 1070,
  NOTIFICATION: 1080,
} as const;

// Breakpoints for responsive design
export const BREAKPOINTS = {
  XS: 480,
  SM: 576,
  MD: 768,
  LG: 992,
  XL: 1200,
  XXL: 1600,
} as const;

// Session management
export const SESSION_CONFIG = {
  DEFAULT_DURATION: 3600, // 1 hour in seconds
  EXTEND_DURATION: 3600, // 1 hour in seconds
  WARNING_THRESHOLD: 300, // 5 minutes in seconds
  UPDATE_INTERVAL: 1000, // 1 second in milliseconds
} as const;

// AI Dialog configuration
export const AI_CONFIG = {
  MAX_MESSAGE_LENGTH: 4000,
  TYPING_DELAY: 50,
  STREAM_CHUNK_SIZE: 10,
} as const;

// Error messages
export const ERROR_MESSAGES = {
  WEBSOCKET_CONNECTION_FAILED: 'Failed to connect to terminal server',
  WEBSOCKET_CONNECTION_LOST: 'Connection to terminal server lost',
  TERMINAL_NOT_READY: 'Terminal is not ready',
  SESSION_EXPIRED: 'Session has expired',
  EXTEND_SESSION_FAILED: 'Failed to extend session',
  AI_REQUEST_FAILED: 'AI request failed',
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  SESSION_EXTENDED: 'Session extended successfully',
  WEBSOCKET_CONNECTED: 'Connected to terminal server',
} as const;

// Local storage keys
export const STORAGE_KEYS = {
  TERMINAL_CONFIG: 'terminal_config',
  KEYBOARD_LAYOUT: 'keyboard_layout',
  UI_THEME: 'ui_theme',
  AI_SETTINGS: 'ai_settings',
} as const;
