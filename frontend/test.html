<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test</title>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    <div id="status">Connecting...</div>
    <div id="messages"></div>
    <input type="text" id="messageInput" placeholder="Type a message...">
    <button onclick="sendMessage()">Send</button>

    <script>
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');

        // Connect to WebSocket
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const backendHost = window.location.hostname + ':8081';
        const wsUrl = `${protocol}//${backendHost}/ws`;
        
        console.log('Connecting to:', wsUrl);
        const socket = new WebSocket(wsUrl);

        socket.onopen = function(event) {
            statusDiv.textContent = 'Connected to WebSocket';
            statusDiv.style.color = 'green';
            console.log('WebSocket connected');
        };

        socket.onmessage = function(event) {
            const message = document.createElement('div');
            message.textContent = 'Received: ' + event.data;
            messagesDiv.appendChild(message);
            console.log('Received:', event.data);
        };

        socket.onclose = function(event) {
            statusDiv.textContent = 'WebSocket connection closed';
            statusDiv.style.color = 'red';
            console.log('WebSocket closed');
        };

        socket.onerror = function(error) {
            statusDiv.textContent = 'WebSocket error';
            statusDiv.style.color = 'red';
            console.error('WebSocket error:', error);
        };

        function sendMessage() {
            const message = messageInput.value;
            if (message && socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({
                    type: 'input',
                    data: message
                }));
                messageInput.value = '';
            }
        }

        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
