[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:重构[frontend/src/services/aiDialogService.js](frontend/src/services/aiDialogService.js) DESCRIPTION:重构目标：1- 该文件太大了，希望分离到不同的模块中去。2- 目前有两个变量：useFrontendAgent、useRefactoredAgent；都可以移除，以及相应的分支代码也做移除，可进一步瘦身；只需要frontendAgent和refactoredAgent两个实现代码保留。
2- 智能体对象支持流式输出，可基于OpenAI SDK的chunk数据结构原样返回。3- 智能体内部封装了多轮工具调用逻辑，由最大工具调用次数限制迭代.采用OpenAI SDK工具API；后续会将xtermService.js中的服务封装为工具。
-[ ] NAME:修复AI对话渲染逻辑 DESCRIPTION:当前渲染的工具消息，会被AI chunk消息冲掉，最后只保留了AI chunk消息。期望工具消息一样，能在最终会话中保留下来。实现 ai消息|工具消息|ai消息|工具消息 的交织。

-[ ] NAME:获取全屏输出的工具可能有BUG DESCRIPTION:可能未准确获得全屏内容，增加相关的日志打印，以便于判断，先尝试修复，如果仍然有问题，后续我将提供日志以便进一步排查；
-[ ] NAME:提示词优化 DESCRIPTION: 要求多查看全屏消息，以便智能体感知当前终端状态
