.PHONY: all clean build-frontend build-backend package dev

# Output directory and port settings
OUTPUT_DIR = dist

# 默认目标
all: package

# 清理构建产物
clean:
	@echo "==> 清理构建产物..."
	rm -rf dist

# 创建必要的目录
dirs:
	@echo "==> 创建目录..."
	@mkdir -p dist/frontend

# 构建前端
build-frontend: dirs
	@echo "==> 构建前端..."
	@cd frontend && npm install && npm run build
	@cp -r frontend/dist/* dist/frontend/

# 构建 Linux/AMD64 后端
build-backend:
	@echo "==> 构建 Linux/AMD64 后端..."
	@cd backend && GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o "../dist/web-terminal" ./cmd

# 打包所有文件
package: build-frontend build-backend
	@echo "==> 构建完成!"
	@echo "应用已打包到: ./dist"
	@echo ""
	@echo "使用方法:"
	@echo "  1. 将 dist 目录复制到 Linux 服务器"
	@echo "  2. 进入 dist 目录"
	@echo "  3. 运行命令: ./web-terminal [选项]"
	@echo ""
	@echo "选项:"
	@echo "  -port=8080          指定服务端口 (默认: 8080)"
	@echo "  -reset-whitelist    重置IP白名单"
	@echo "  -runtime=60         设置运行时间(分钟)"
	@echo "  -no-expiry          禁用运行时间限制"
	@echo "  -frontend=./frontend 指定前端资源目录"

# 开发服务器
dev:
	@echo "==> 启动开发服务器..."
	@cd frontend && npm run build & \
	 cd backend && go run cmd/main.go
