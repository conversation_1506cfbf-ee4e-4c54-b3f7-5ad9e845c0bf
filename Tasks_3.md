[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:重构frontendAIAgent.js中智能体方案 DESCRIPTION:重构目标：1- 封装出一个智能体类型：由系统提示词、sessionId（session可基于当前的实现，不做调整）、大模型、工具、最大工具调用迭代次数（默认：30）等构成；
2- 智能体对象支持流式输出，可基于OpenAI SDK的chunk数据结构原样返回。3- 智能体内部封装了多轮工具调用逻辑，由最大工具调用次数限制迭代.采用OpenAI SDK工具API；后续会将xtermService.js中的服务封装为工具。
-[ ] NAME:AI对话框支持流式数据 DESCRIPTION:AI对话框支持智能体返回的流式数据：AI chunk message，Tool chunk message，实现AI回复的流式效果、工具调用的展示效果；

-[ ] NAME:xtermService.js 封装为智能体工具 DESCRIPTION:将xtermService.js相关服务封装为一个智能体输入工具函数：入参可以设定为（输入模式：单键、 组合键、命令行、 命令行组； 单键值（可选）； 组合键值（可选）；命令行值（可选）； 命令行组值（可选）； 选定模式下值必填）； 和一个获取屏幕输出的工具函数：可以获取单行、多行（给的行数，返回最近行数）、全屏内容；
-[ ] NAME:封装一个等待工具函数 DESCRIPTION: 智能体可以调用该函数，等待指定的时间，以实现某些需等待的场景。
-[ ] NAME:重构以上AI智能体的系统提示词 DESCRIPTION: 提示词包含以下信息， 你是一个交互式终端（macos系统/bin/bash）自动化专家，正在与一个交互式终端协同工作，努力完成用户提交的任务。你有一个向终端输入和获取终端输出的工具箱，要灵活运用该工具，尽最大努力完成用户提交的任务，尽量自主决策，不要过多向用户询问。例如你可以精准使用工具完成：
场景一：ctrl + c 中断某个正在运行的程序
使用输入工具的组合键模式，输入ctrl+c
场景二：寻找文件内容进行编辑
使用输入工具的命令行模式，输入vi /tmp/nginx.conf
合理使用等待工具，等待vi 编辑文件就绪
获取全屏输出，找到光标位置和目标字符串
未找到时，使用输入工具，切换下一页
找到以后，使用命令行组输入多个右移键，移动到目标字符位置
等等
场景三：ssh连接到远程服务器
输入ssh 连接命令
查看输出，是否包含受信主机
包含则输入yes
否则输入密码
执行远程命令