[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:修复虚拟键盘问题 DESCRIPTION:前端工程中，index.html 里的 keyboardBtn 按钮用于唤起虚拟键盘，但是目前点击后，无法正常唤起，浏览器控制台亦无报错信息；需要你修复该问题，保证键盘正常唤起。
你甚至可以建议更好的虚拟键盘库，将当前虚拟键盘代码完全替换
-[ ] NAME:实现虚拟键盘输入与xterm的集成 DESCRIPTION:虚拟键盘上的按键，要输出到当前xterm的终端中。
实现简单场景：如，虚拟键盘上键入`ls -lrt` 要显示在 xterm 的终端界面，虚拟键盘回车，xterm 执行命令并输出结果
实现按键组合：如，虚拟键盘上键入`ctrl + c` 可以中断xterm中正在执行的命令
实现方向键等：如，虚拟键盘上键入`left` 可以将光标左移一位
-[ ] NAME:封装一个xterm界面输入输出的服务 DESCRIPTION:该服务封装了一系列工具方法，支持向xterm中输入命令：如单个按键（字母数字字符、方向键、回车、esc）、组合按键（如：ctrl + c）、命令行（如：ls -lrt）、命令行数组（如：['ls -lrt', 'cd /home']）。获取xterm中的输出内容：如获取当前光标所在行的内容、获取指定行的内容、获取指定行范围的内容、获取所有内容。
-[ ] NAME:在index.html的keyboardBtn按钮旁，增加一个AI按钮，可唤起AI对话框 DESCRIPTION:唤起AI对话，AI对话组件可以选用已有框架或自行实现，要求用户和AI回复内容均支持markdown渲染，用户消息输入框支持多行消息，多行内容自动撑高消息框。发送按钮采用小图标，单独一行，紧凑使用空间。
-[ ] NAME:新建AI智能体包 DESCRIPTION:在新包中，实现一个AI智能体，可以引入外部框架如langgraph构建智能体，也可以选择从0开发；模型API支持OpenAI接口。
-[ ] NAME:实现AI对话框中与AI智能体的交互 DESCRIPTION:基于前面完成的功能，将AI对话框与AI智能体集成，实现在对话框中与AI智能体进行交互。
-[ ] NAME:将AI对话框与xterm界面输入输出的服务进行集成 DESCRIPTION:在AI对话框中，用户可以输入命令，AI智能体可以输出命令，最终由xterm界面输入输出的服务，将命令输入到xterm中，并将输出内容返回给AI智能体。