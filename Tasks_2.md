[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:修复虚拟键盘问题 DESCRIPTION:前端工程中，index.html 里的 keyboardBtn 按钮用于唤起虚拟键盘，我希望
换一套虚拟键盘库，将当前虚拟键盘代码完全替换
-[ ] NAME:由于已经更换了虚拟键盘库，需要重新实现新虚拟键盘输入与xterm的集成 DESCRIPTION:虚拟键盘上的按键，要输出到当前xterm的终端中。
实现简单场景：如，虚拟键盘上键入`ls -lrt` 要显示在 xterm 的终端界面，虚拟键盘回车，xterm 执行命令并输出结果
实现按键组合：如，虚拟键盘上键入`ctrl + c` 可以中断xterm中正在执行的命令
实现方向键等：如，虚拟键盘上键入`left` 可以将光标左移一位
-[ ] NAME:重构AI智能体包 DESCRIPTION:当前AI智能体包移除，新写在frontend包中，实现一个前端的智能体，无需考虑安全问题。功能与当前一致，调用OpenAI的接口要求允许配置 chat 接口端点、模型ID、APIKEY，我后续会手动替换为其它OpenAI兼容接口。
-[ ] NAME:AI对话框功能调整 DESCRIPTION:AI对话框悬浮在xterm终端界面上，但同时允许用户与xterm交互。允许调整AI对话框大小、允许隐藏和显示。
-[ ] NAME:由于AI智能体被重构，需重新将AI对话框与xterm界面输入输出的服务进行集成 DESCRIPTION:在AI对话框中，用户可以输入命令，AI智能体可以输出命令，最终由xterm界面输入输出的服务，将命令输入到xterm中，并将输出内容返回给AI智能体。